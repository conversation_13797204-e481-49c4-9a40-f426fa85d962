{"agents": ["Augment CLI", "<PERSON>", "Cursor CLI"], "runs_per_prompt": 3, "parallel_agents": false, "output_filename": "llm_evaluator_results", "stage_dir": "./stage", "repo_url": "https://github.com/example/sample-repo.git", "branch": "main", "metrics": ["response_time", "diff_metrics", "completeness", "technical_correctness", "functional_correctness", "clarity", "instruction_adherence"], "agent_config": {"Cursor CLI": {"model": "claude-4-sonnet", "timeout": 600000}, "Augment CLI": {"timeout": 1200000}, "Claude Code": {"timeout": 600000}}, "mode": "LLM_Evaluator", "LLM_Evaluator": {"generate_prompts": true, "prompt_topics": ["Create comprehensive documentation for the main entry point", "Add unit tests for core functionality", "Implement error handling and logging", "Add input validation and sanitization"]}}