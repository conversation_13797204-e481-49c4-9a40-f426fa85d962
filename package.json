{"name": "augbench", "version": "1.0.0", "description": "AI Coding Assistant Benchmarking Tool - Compare agentic AI CLI tools on code repositories", "main": "bin/augbench.js", "bin": {"augbench": "./bin/augbench.js"}, "scripts": {"start": "node bin/augbench.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "validate": "node bin/augbench.js validate", "benchmark": "node bin/augbench.js benchmark", "help": "node bin/augbench.js help"}, "keywords": ["ai", "benchmark", "cli", "coding-assistant", "augment", "claude", "cursor", "evaluation"], "author": "Augment Solutions", "license": "MIT", "engines": {"node": ">=22.0.0"}, "dependencies": {"joi": "^17.11.0", "fs-extra": "^11.2.0", "simple-git": "^3.20.0", "tree-sitter": "^0.20.4", "axios": "^1.6.2", "chartjs-node-canvas": "^4.1.6", "chart.js": "^4.4.0", "commander": "^11.1.0", "dotenv": "^16.3.1", "chalk": "^4.1.2", "ora": "^5.4.1", "inquirer": "^8.2.6"}, "devDependencies": {"jest": "^29.7.0", "@types/jest": "^29.5.8", "eslint": "^8.54.0", "prettier": "^3.1.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/tests/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "repository": {"type": "git", "url": "https://github.com/augment-solutions/augbench.git"}, "bugs": {"url": "https://github.com/augment-solutions/augbench/issues"}, "homepage": "https://github.com/augment-solutions/augbench#readme"}