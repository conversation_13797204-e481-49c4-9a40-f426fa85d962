#!/usr/bin/env node

/**
 * Augbench CLI Entry Point
 * AI Coding Assistant Benchmarking Tool
 */

const path = require('path');
const { BenchmarkCLI } = require('../src/cli/BenchmarkCLI');

// Ensure we're using the correct Node.js version
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 22) {
  console.error(`Error: Node.js version ${nodeVersion} is not supported.`);
  console.error('Augbench requires Node.js >= 22.0.0');
  console.error('Please upgrade your Node.js installation.');
  process.exit(1);
}

// Handle uncaught exceptions gracefully
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Initialize and run CLI
async function main() {
  try {
    const cli = new BenchmarkCLI();
    await cli.run(process.argv);
  } catch (error) {
    console.error('Fatal error:', error.message);
    if (process.env.DEBUG) {
      console.error('Stack:', error.stack);
    }
    process.exit(1);
  }
}

// Run the CLI
main();
