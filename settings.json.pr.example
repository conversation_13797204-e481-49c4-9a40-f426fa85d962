{"agents": ["Augment CLI", "<PERSON>"], "runs_per_prompt": 1, "parallel_agents": true, "output_filename": "pr_recreate_results", "stage_dir": "./stage", "repo_url": "https://github.com/example/target-repo.git", "branch": "main", "metrics": ["response_time", "diff_metrics", "ast_similarity", "completeness", "technical_correctness", "functional_correctness", "clarity", "instruction_adherence"], "agent_config": {"Augment CLI": {"timeout": 1800000}, "Claude Code": {"timeout": 1200000}}, "mode": "PR_Recreate", "PR_Recreate": {"num_prs": 5}}