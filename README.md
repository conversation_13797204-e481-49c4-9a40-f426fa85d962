# Augbench - AI Coding Assistant Benchmarking Tool

A Node.js CLI tool for benchmarking agentic AI coding assistants on real code repositories.

## Overview

Augbench compares AI coding assistants (Augment CLI, Claude Code, Cursor CLI) across two operational modes:
- **LLM_Evaluator**: Test agents on custom prompts and coding tasks
- **PR_Recreate**: Benchmark agents by recreating actual Pull Requests from repository history

## Quick Start

### Installation

```bash
npm install -g augbench
```

### Setup

1. Copy configuration template:
```bash
cp settings.json.llm.example settings.json  # For LLM_Evaluator mode
# OR
cp settings.json.pr.example settings.json   # For PR_Recreate mode
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your API keys
```

### Usage

```bash
# Validate prerequisites and configuration
augbench validate

# Run benchmark based on settings.json
augbench benchmark

# Show help and examples
augbench help
```

## Features

- **Two Operational Modes**: LLM_Evaluator and PR_Recreate
- **Multiple AI Agents**: Support for Augment CLI, Claude <PERSON>, Cursor CLI
- **Comprehensive Metrics**: Response time, diff analysis, AST similarity, LLM-assessed quality metrics
- **Parallel Execution**: Run multiple agents concurrently
- **Visual Reports**: Automatic PNG chart generation
- **Cross-Platform**: Windows, Mac, Linux support

## Documentation

- [Installation & Usage](docs/Installation_Usage.md)
- [Operational Modes](docs/Modes.md)
- [Metrics System](docs/Metrics.md)
- [Supported Assistants](docs/Assistants.md)
- [Testing Guide](docs/Testing.md)

## Requirements

- Node.js >= 22.0.0
- Git >= 2.30.0
- At least one supported AI assistant CLI tool
- 10GB+ available disk space

## License

MIT License - see LICENSE file for details.
