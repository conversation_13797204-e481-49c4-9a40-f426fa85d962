const { AugmentCLIAdapter } = require('./AugmentCLIAdapter');
const { ClaudeCodeAdapter } = require('./ClaudeCodeAdapter');
const { CursorCLIAdapter } = require('./CursorCLIAdapter');

/**
 * Adapter Factory - Creates and manages AI assistant adapters
 */
class AdapterFactory {
  constructor() {
    this.adapters = new Map();
    this.registerDefaultAdapters();
  }

  /**
   * Register default adapters
   */
  registerDefaultAdapters() {
    this.registerAdapter('Augment CLI', AugmentCLIAdapter);
    this.registerAdapter('Claude Code', ClaudeCodeAdapter);
    this.registerAdapter('Cursor CLI', CursorCLIAdapter);
  }

  /**
   * Register a new adapter class
   * @param {string} name - Adapter name
   * @param {Class} AdapterClass - Adapter class constructor
   */
  registerAdapter(name, AdapterClass) {
    this.adapters.set(name, AdapterClass);
  }

  /**
   * Create an adapter instance
   * @param {string} name - Adapter name
   * @param {Object} config - Adapter configuration
   * @returns {BaseAdapter} Adapter instance
   */
  createAdapter(name, config = {}) {
    const AdapterClass = this.adapters.get(name);
    
    if (!AdapterClass) {
      throw new Error(`Unknown adapter: ${name}. Available adapters: ${this.getAvailableAdapters().join(', ')}`);
    }

    try {
      return new AdapterClass(config);
    } catch (error) {
      throw new Error(`Failed to create adapter ${name}: ${error.message}`);
    }
  }

  /**
   * Create multiple adapter instances
   * @param {Array<string>} names - Array of adapter names
   * @param {Object} globalConfig - Global configuration for all adapters
   * @param {Object} specificConfigs - Adapter-specific configurations
   * @returns {Array<BaseAdapter>} Array of adapter instances
   */
  createAdapters(names, globalConfig = {}, specificConfigs = {}) {
    const adapters = [];
    
    for (const name of names) {
      const adapterConfig = {
        ...globalConfig,
        ...specificConfigs[name]
      };
      
      try {
        const adapter = this.createAdapter(name, adapterConfig);
        adapters.push(adapter);
      } catch (error) {
        throw new Error(`Failed to create adapter ${name}: ${error.message}`);
      }
    }
    
    return adapters;
  }

  /**
   * Get list of available adapter names
   * @returns {Array<string>} Available adapter names
   */
  getAvailableAdapters() {
    return Array.from(this.adapters.keys());
  }

  /**
   * Check if an adapter is registered
   * @param {string} name - Adapter name
   * @returns {boolean} True if adapter is registered
   */
  hasAdapter(name) {
    return this.adapters.has(name);
  }

  /**
   * Validate that all requested adapters are available
   * @param {Array<string>} names - Array of adapter names to validate
   * @returns {Object} Validation result
   */
  validateAdapters(names) {
    const results = {
      valid: true,
      missing: [],
      available: this.getAvailableAdapters()
    };

    for (const name of names) {
      if (!this.hasAdapter(name)) {
        results.valid = false;
        results.missing.push(name);
      }
    }

    return results;
  }

  /**
   * Get adapter information
   * @param {string} name - Adapter name
   * @returns {Object} Adapter information
   */
  getAdapterInfo(name) {
    const AdapterClass = this.adapters.get(name);
    
    if (!AdapterClass) {
      return null;
    }

    // Create a temporary instance to get information
    try {
      const tempAdapter = new AdapterClass();
      return {
        name: name,
        command: tempAdapter.getCommand(),
        className: AdapterClass.name,
        defaultConfig: tempAdapter.getConfig()
      };
    } catch (error) {
      return {
        name: name,
        className: AdapterClass.name,
        error: error.message
      };
    }
  }

  /**
   * Get information for all registered adapters
   * @returns {Array<Object>} Array of adapter information
   */
  getAllAdapterInfo() {
    return this.getAvailableAdapters().map(name => this.getAdapterInfo(name));
  }

  /**
   * Validate adapter configurations
   * @param {Object} configs - Adapter configurations
   * @returns {Object} Validation results
   */
  validateConfigurations(configs) {
    const results = {
      valid: true,
      errors: []
    };

    for (const [adapterName, config] of Object.entries(configs)) {
      if (!this.hasAdapter(adapterName)) {
        results.valid = false;
        results.errors.push(`Unknown adapter: ${adapterName}`);
        continue;
      }

      // Validate configuration structure
      if (config && typeof config !== 'object') {
        results.valid = false;
        results.errors.push(`Invalid configuration for ${adapterName}: must be an object`);
        continue;
      }

      // Validate timeout if specified
      if (config.timeout !== undefined) {
        if (typeof config.timeout !== 'number' || config.timeout < 1000) {
          results.valid = false;
          results.errors.push(`Invalid timeout for ${adapterName}: must be a number >= 1000`);
        }
      }
    }

    return results;
  }

  /**
   * Create adapters from settings configuration
   * @param {Object} settings - Settings object from SettingsManager
   * @returns {Array<BaseAdapter>} Array of configured adapter instances
   */
  createAdaptersFromSettings(settings) {
    const { agents, agent_config = {} } = settings;
    
    // Validate that all requested agents are available
    const validation = this.validateAdapters(agents);
    if (!validation.valid) {
      throw new Error(`Unknown agents: ${validation.missing.join(', ')}. Available: ${validation.available.join(', ')}`);
    }

    // Validate configurations
    const configValidation = this.validateConfigurations(agent_config);
    if (!configValidation.valid) {
      throw new Error(`Configuration validation failed:\n${configValidation.errors.join('\n')}`);
    }

    // Create adapters with their specific configurations
    return this.createAdapters(agents, {}, agent_config);
  }
}

// Export singleton instance
module.exports = { AdapterFactory: new AdapterFactory() };
