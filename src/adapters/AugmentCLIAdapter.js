const { BaseAdapter } = require('./BaseAdapter');
const { spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

/**
 * Augment CLI Adapter - Implementation for Augment CLI (auggie)
 * Handles execution of prompts using the Augment CLI tool
 */
class AugmentCLIAdapter extends BaseAdapter {
  constructor(config = {}) {
    super(config);
    this.command = 'auggie';
  }

  getCommand() {
    return this.command;
  }

  async execute(prompt, context) {
    const startTime = Date.now();

    try {
      this.log('info', 'Starting Augment CLI execution', {
        workingDirectory: context.workingDirectory,
        mode: context.mode
      });

      // Sanitize and prepare prompt
      const sanitizedPrompt = this.sanitizePrompt(prompt);

      // Prepare execution environment
      const env = await this.prepareEnvironment(context);

      // Create prompt file for auggie
      const promptFile = await this.createPromptFile(sanitizedPrompt, context);

      try {
        // Execute auggie command
        const result = await this.executeAuggie(promptFile, env);

        const executionTime = Date.now() - startTime;
        this.log('info', 'Augment CLI execution completed', {
          executionTime,
          success: true
        });

        return this.parseOutput(result.output, {
          executionTime,
          success: true,
          exitCode: result.exitCode,
          command: this.buildCommand(promptFile)
        });

      } finally {
        // Clean up prompt file
        await this.cleanupPromptFile(promptFile);
      }

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.log('error', 'Augment CLI execution failed', {
        error: error.message,
        executionTime
      });

      return this.handleError(error, context);
    }
  }

  async validate() {
    try {
      // Check if auggie command is available
      const result = await this.executeCommand('which', [this.command], {
        timeout: 5000
      });

      if (result.exitCode !== 0) {
        return {
          valid: false,
          message: `${this.command} not found in PATH`
        };
      }

      // Check auggie version/help to ensure it's working
      const versionResult = await this.executeCommand(this.command, ['--help'], {
        timeout: 10000
      });

      return {
        valid: versionResult.exitCode === 0,
        message: versionResult.exitCode === 0
          ? `${this.command} is available and working`
          : `${this.command} found but not responding correctly`
      };

    } catch (error) {
      return {
        valid: false,
        message: `Validation failed: ${error.message}`
      };
    }
  }

  async createPromptFile(prompt, context) {
    const tempDir = path.join(context.workingDirectory || process.cwd(), '.augbench-temp');
    await fs.ensureDir(tempDir);

    const promptFile = path.join(tempDir, `prompt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}.md`);
    await fs.writeFile(promptFile, prompt, 'utf8');

    return promptFile;
  }

  async cleanupPromptFile(promptFile) {
    try {
      if (await fs.pathExists(promptFile)) {
        await fs.remove(promptFile);
      }

      // Also clean up temp directory if empty
      const tempDir = path.dirname(promptFile);
      const files = await fs.readdir(tempDir);
      if (files.length === 0) {
        await fs.remove(tempDir);
      }
    } catch (error) {
      this.log('warn', 'Failed to cleanup prompt file', {
        promptFile,
        error: error.message
      });
    }
  }

  buildCommand(promptFile) {
    return `${this.command} "${promptFile}"`;
  }

  async executeAuggie(promptFile, env) {
    const args = [promptFile];
    return await this.executeCommand(this.command, args, env);
  }

  async executeCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      const timeout = options.timeout || this.config.timeout;
      const cwd = options.cwd || process.cwd();
      const env = options.env || process.env;

      let output = '';
      let errorOutput = '';

      const child = spawn(command, args, {
        cwd,
        env,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      // Set up timeout
      const timeoutId = setTimeout(() => {
        child.kill('SIGTERM');
        reject(new Error(`Command timed out after ${timeout}ms`));
      }, timeout);

      // Collect output
      child.stdout.on('data', (data) => {
        output += data.toString();
      });

      child.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      child.on('close', (exitCode) => {
        clearTimeout(timeoutId);

        resolve({
          output: output.trim(),
          errorOutput: errorOutput.trim(),
          exitCode
        });
      });

      child.on('error', (error) => {
        clearTimeout(timeoutId);
        reject(error);
      });
    });
  }
}

module.exports = { AugmentCLIAdapter };
