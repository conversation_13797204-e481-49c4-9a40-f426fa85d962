const { BaseAdapter } = require('./BaseAdapter');
const { spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

/**
 * Cursor CLI Adapter - Implementation for Cursor CLI (cursor-agent)
 * Handles execution of prompts using the Cursor CLI tool
 */
class CursorCLIAdapter extends BaseAdapter {
  constructor(config = {}) {
    super(config);
    this.command = 'cursor-agent';
  }

  getCommand() {
    return this.command;
  }

  async execute(prompt, context) {
    const startTime = Date.now();

    try {
      this.log('info', 'Starting Cursor CLI execution', {
        workingDirectory: context.workingDirectory,
        mode: context.mode
      });

      // Sanitize and prepare prompt
      const sanitizedPrompt = this.sanitizePrompt(prompt);

      // Prepare execution environment
      const env = await this.prepareEnvironment(context);

      // Execute cursor-agent command
      const result = await this.executeCursor(sanitizedPrompt, env);

      const executionTime = Date.now() - startTime;
      this.log('info', 'Cursor CLI execution completed', {
        executionTime,
        success: true
      });

      return this.parseOutput(result.output, {
        executionTime,
        success: true,
        exitCode: result.exitCode,
        command: this.buildCommand(sanitizedPrompt)
      });

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.log('error', 'Cursor CLI execution failed', {
        error: error.message,
        executionTime
      });

      return this.handleError(error, context);
    }
  }

  async validate() {
    try {
      // Check if cursor-agent command is available
      const result = await this.executeCommand('which', [this.command], {
        timeout: 5000
      });

      if (result.exitCode !== 0) {
        return {
          valid: false,
          message: `${this.command} not found in PATH`
        };
      }

      // Check cursor-agent version/help to ensure it's working
      const versionResult = await this.executeCommand(this.command, ['--help'], {
        timeout: 10000
      });

      return {
        valid: versionResult.exitCode === 0,
        message: versionResult.exitCode === 0
          ? `${this.command} is available and working`
          : `${this.command} found but not responding correctly`
      };

    } catch (error) {
      return {
        valid: false,
        message: `Validation failed: ${error.message}`
      };
    }
  }

  buildCommand(prompt) {
    return `${this.command} "${prompt.replace(/"/g, '\\"')}"`;
  }

  async executeCursor(prompt, env) {
    const args = [];

    // Add model configuration if specified
    if (this.config.model) {
      args.push('--model', this.config.model);
    }

    // Add prompt as argument or via stdin depending on Cursor CLI interface
    args.push('--prompt', prompt);

    return await this.executeCommand(this.command, args, env);
  }

  async executeCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      const timeout = options.timeout || this.config.timeout;
      const cwd = options.cwd || process.cwd();
      const env = options.env || process.env;

      let output = '';
      let errorOutput = '';

      const child = spawn(command, args, {
        cwd,
        env,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      // Set up timeout
      const timeoutId = setTimeout(() => {
        child.kill('SIGTERM');
        reject(new Error(`Command timed out after ${timeout}ms`));
      }, timeout);

      // Collect output
      child.stdout.on('data', (data) => {
        output += data.toString();
      });

      child.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      child.on('close', (exitCode) => {
        clearTimeout(timeoutId);

        resolve({
          output: output.trim(),
          errorOutput: errorOutput.trim(),
          exitCode
        });
      });

      child.on('error', (error) => {
        clearTimeout(timeoutId);
        reject(error);
      });
    });
  }
}

module.exports = { CursorCLIAdapter };
