/**
 * Base Adapter - Abstract class for AI assistant adapters
 * Defines the standardized interface for all agent implementations
 */
class BaseAdapter {
  constructor(config = {}) {
    this.config = {
      timeout: 600000, // 10 minutes default
      ...config
    };
    this.name = this.constructor.name.replace('Adapter', '');
  }

  /**
   * Abstract method to execute a prompt with the AI assistant
   * Must be implemented by concrete adapter classes
   * 
   * @param {string} prompt - The prompt/instruction to execute
   * @param {Object} context - Execution context
   * @param {string} context.workingDirectory - Directory to execute in
   * @param {Object} context.environment - Environment variables
   * @param {string} context.mode - Execution mode (LLM_Evaluator or PR_Recreate)
   * @returns {Promise<Object>} Result object with output and metadata
   */
  async execute(prompt, context) {
    throw new Error(`execute() method must be implemented by ${this.constructor.name}`);
  }

  /**
   * Validate that the agent tool is available and configured
   * @returns {Promise<Object>} Validation result
   */
  async validate() {
    throw new Error(`validate() method must be implemented by ${this.constructor.name}`);
  }

  /**
   * Get the command name for this adapter
   * @returns {string} Command name
   */
  getCommand() {
    throw new Error(`getCommand() method must be implemented by ${this.constructor.name}`);
  }

  /**
   * Prepare the execution environment
   * @param {Object} context - Execution context
   * @returns {Promise<Object>} Prepared environment
   */
  async prepareEnvironment(context) {
    return {
      cwd: context.workingDirectory || process.cwd(),
      env: {
        ...process.env,
        ...context.environment
      },
      timeout: this.config.timeout
    };
  }

  /**
   * Parse and normalize the agent's output
   * @param {string} rawOutput - Raw output from the agent
   * @param {Object} metadata - Execution metadata
   * @returns {Object} Normalized result
   */
  parseOutput(rawOutput, metadata) {
    return {
      output: rawOutput,
      metadata: {
        agent: this.name,
        timestamp: new Date().toISOString(),
        executionTime: metadata.executionTime || 0,
        success: metadata.success !== false,
        error: metadata.error || null,
        ...metadata
      }
    };
  }

  /**
   * Handle execution errors gracefully
   * @param {Error} error - The error that occurred
   * @param {Object} context - Execution context
   * @returns {Object} Error result
   */
  handleError(error, context) {
    return {
      output: '',
      metadata: {
        agent: this.name,
        timestamp: new Date().toISOString(),
        success: false,
        error: {
          message: error.message,
          code: error.code || 'UNKNOWN_ERROR',
          stack: error.stack
        },
        context: {
          workingDirectory: context.workingDirectory,
          mode: context.mode
        }
      }
    };
  }

  /**
   * Create a timeout promise for execution
   * @param {number} timeoutMs - Timeout in milliseconds
   * @returns {Promise} Timeout promise
   */
  createTimeoutPromise(timeoutMs) {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Agent execution timed out after ${timeoutMs}ms`));
      }, timeoutMs);
    });
  }

  /**
   * Log execution details
   * @param {string} level - Log level (info, warn, error)
   * @param {string} message - Log message
   * @param {Object} data - Additional data to log
   */
  log(level, message, data = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      agent: this.name,
      message,
      ...data
    };
    
    // In a full implementation, this would use a proper logger
    console.log(`[${timestamp}] [${level.toUpperCase()}] [${this.name}] ${message}`, 
                data && Object.keys(data).length > 0 ? data : '');
  }

  /**
   * Sanitize prompt for safe execution
   * @param {string} prompt - Raw prompt
   * @returns {string} Sanitized prompt
   */
  sanitizePrompt(prompt) {
    if (typeof prompt !== 'string') {
      throw new Error('Prompt must be a string');
    }
    
    // Basic sanitization - remove potentially dangerous characters
    return prompt
      .replace(/[`$]/g, '') // Remove backticks and dollar signs
      .trim();
  }

  /**
   * Get adapter configuration
   * @returns {Object} Current configuration
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * Update adapter configuration
   * @param {Object} newConfig - New configuration options
   */
  updateConfig(newConfig) {
    this.config = {
      ...this.config,
      ...newConfig
    };
  }
}

module.exports = { BaseAdapter };
