const { LLMAssessedMetric } = require('./LLMAssessedMetric');

/**
 * Technical Correctness Metric - Evaluates technical accuracy and best practices
 */
class TechnicalCorrectnessMetric extends LLMAssessedMetric {
  constructor(config = {}) {
    super({
      name: 'technical_correctness',
      ...config
    });
  }

  /**
   * Get metric description
   * @returns {string} Metric description
   */
  getDescription() {
    return 'Evaluates technical accuracy, syntax correctness, and adherence to best practices';
  }

  /**
   * Validate input data for technical correctness evaluation
   * @param {Object} data - Data containing code/response to evaluate
   * @param {Object} context - Evaluation context
   * @returns {boolean} True if data is valid
   */
  validateInput(data, context) {
    if (!super.validateInput(data, context)) {
      return false;
    }

    const hasCode = data.code !== undefined;
    const hasResponse = data.response !== undefined;
    const hasOutput = data.output !== undefined;
    const hasImplementation = data.implementation !== undefined;

    return hasCode || hasResponse || hasOutput || hasImplementation;
  }

  /**
   * Generate evaluation prompt for technical correctness assessment
   * @param {Object} data - Data containing code/response to evaluate
   * @param {Object} context - Evaluation context
   * @returns {Promise<string>} Evaluation prompt
   */
  async generatePrompt(data, context) {
    const systemPrompt = this.buildSystemPrompt();
    
    let codeToEvaluate;
    
    if (data.code) {
      codeToEvaluate = data.code;
    } else if (data.response) {
      codeToEvaluate = data.response;
    } else if (data.output) {
      codeToEvaluate = data.output;
    } else if (data.implementation) {
      codeToEvaluate = data.implementation;
    } else {
      throw new Error('No valid code/response found for evaluation');
    }

    const evaluationPrompt = `${systemPrompt}

CODE/RESPONSE TO EVALUATE:
${codeToEvaluate}

Please evaluate the technical correctness of this code/response. Consider:
1. Syntax correctness and proper language usage
2. Adherence to coding best practices and conventions
3. Proper error handling and edge case management
4. Code structure, organization, and maintainability
5. Security considerations and potential vulnerabilities
6. Performance implications and efficiency
7. Proper use of APIs, libraries, and frameworks

Provide a score from 0-10 where:
- 10: Technically excellent with perfect syntax, best practices, and no issues
- 8-9: Very good technical quality with minor issues or improvements possible
- 6-7: Good technical quality but with some notable issues or suboptimal practices
- 4-5: Adequate technical quality but with several issues that should be addressed
- 2-3: Poor technical quality with significant issues and bad practices
- 0-1: Severely flawed with major technical errors or completely incorrect

Score:`;

    return evaluationPrompt;
  }

  /**
   * Get evaluation criteria for technical correctness
   * @returns {Array<string>} Array of evaluation criteria
   */
  getEvaluationCriteria() {
    return [
      'Syntax is correct and follows language conventions',
      'Code follows established best practices and design patterns',
      'Proper error handling and input validation',
      'Good code structure, readability, and maintainability',
      'Efficient algorithms and data structures',
      'Security best practices are followed',
      'Proper use of libraries, APIs, and frameworks',
      'Code is well-organized with appropriate separation of concerns'
    ];
  }

  /**
   * Get evaluation examples for technical correctness
   * @returns {Array<Object>} Array of examples with scores
   */
  getEvaluationExamples() {
    return [
      {
        score: 10,
        description: 'Perfect syntax, follows all best practices, excellent error handling, secure, efficient, and well-structured'
      },
      {
        score: 8,
        description: 'Very good code quality with proper syntax and practices, minor optimization opportunities'
      },
      {
        score: 6,
        description: 'Good code that works but has some style issues, missing error handling, or suboptimal patterns'
      },
      {
        score: 4,
        description: 'Code works but has several technical issues, poor practices, or potential bugs'
      },
      {
        score: 2,
        description: 'Code has significant technical problems, bad practices, or security vulnerabilities'
      },
      {
        score: 0,
        description: 'Code has major syntax errors, completely incorrect implementation, or dangerous practices'
      }
    ];
  }
}

module.exports = { TechnicalCorrectnessMetric };
