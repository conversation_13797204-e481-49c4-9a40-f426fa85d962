const { LLMAssessedMetric } = require('./LLMAssessedMetric');

/**
 * Clarity Metric - Evaluates code readability, documentation, and communication clarity
 */
class ClarityMetric extends LLMAssessedMetric {
  constructor(config = {}) {
    super({
      name: 'clarity',
      ...config
    });
  }

  /**
   * Get metric description
   * @returns {string} Metric description
   */
  getDescription() {
    return 'Evaluates code readability, documentation quality, and overall communication clarity';
  }

  /**
   * Validate input data for clarity evaluation
   * @param {Object} data - Data containing content to evaluate
   * @param {Object} context - Evaluation context
   * @returns {boolean} True if data is valid
   */
  validateInput(data, context) {
    if (!super.validateInput(data, context)) {
      return false;
    }

    const hasCode = data.code !== undefined;
    const hasResponse = data.response !== undefined;
    const hasOutput = data.output !== undefined;
    const hasExplanation = data.explanation !== undefined;
    const hasDocumentation = data.documentation !== undefined;

    return hasCode || hasResponse || hasOutput || hasExplanation || hasDocumentation;
  }

  /**
   * Generate evaluation prompt for clarity assessment
   * @param {Object} data - Data containing content to evaluate
   * @param {Object} context - Evaluation context
   * @returns {Promise<string>} Evaluation prompt
   */
  async generatePrompt(data, context) {
    const systemPrompt = this.buildSystemPrompt();
    
    let contentToEvaluate;
    
    if (data.code) {
      contentToEvaluate = data.code;
    } else if (data.response) {
      contentToEvaluate = data.response;
    } else if (data.output) {
      contentToEvaluate = data.output;
    } else if (data.explanation) {
      contentToEvaluate = data.explanation;
    } else if (data.documentation) {
      contentToEvaluate = data.documentation;
    } else {
      throw new Error('No valid content found for clarity evaluation');
    }

    const evaluationPrompt = `${systemPrompt}

CONTENT TO EVALUATE:
${contentToEvaluate}

Please evaluate the clarity and readability of this content. Consider:
1. Is the code/text easy to read and understand?
2. Are variable names, function names, and comments descriptive and meaningful?
3. Is the structure and organization logical and clear?
4. Are complex concepts explained adequately?
5. Is the documentation comprehensive and helpful?
6. Would a developer be able to understand and maintain this code easily?
7. Is the communication style clear and professional?

Provide a score from 0-10 where:
- 10: Exceptionally clear, well-documented, and easy to understand
- 8-9: Very clear with good naming, structure, and documentation
- 6-7: Generally clear but could benefit from better naming or documentation
- 4-5: Somewhat unclear with confusing parts or poor documentation
- 2-3: Difficult to understand with poor naming and minimal documentation
- 0-1: Very unclear, confusing, or completely undocumented

Score:`;

    return evaluationPrompt;
  }

  /**
   * Get evaluation criteria for clarity
   * @returns {Array<string>} Array of evaluation criteria
   */
  getEvaluationCriteria() {
    return [
      'Code is easy to read and understand',
      'Variable and function names are descriptive and meaningful',
      'Code structure and organization are logical',
      'Comments and documentation are comprehensive and helpful',
      'Complex logic is explained clearly',
      'Consistent coding style and formatting',
      'Appropriate level of detail for the intended audience',
      'Clear communication without ambiguity'
    ];
  }

  /**
   * Get evaluation examples for clarity
   * @returns {Array<Object>} Array of examples with scores
   */
  getEvaluationExamples() {
    return [
      {
        score: 10,
        description: 'Code is exceptionally clear with perfect naming, comprehensive documentation, and excellent structure'
      },
      {
        score: 8,
        description: 'Very readable code with good naming conventions, adequate documentation, and clear organization'
      },
      {
        score: 6,
        description: 'Generally readable but some unclear variable names, missing comments, or confusing structure'
      },
      {
        score: 4,
        description: 'Somewhat difficult to follow with poor naming, minimal documentation, or unclear organization'
      },
      {
        score: 2,
        description: 'Hard to understand with cryptic names, no documentation, and poor structure'
      },
      {
        score: 0,
        description: 'Completely unclear, unreadable, or incomprehensible code/content'
      }
    ];
  }
}

module.exports = { ClarityMetric };
