const { DeterministicMetric } = require('./DeterministicMetric');
const Parser = require('tree-sitter');

/**
 * AST Similarity Metric - Compares code structure using Abstract Syntax Trees
 * Uses tree-sitter for parsing and structural comparison
 */
class ASTSimilarityMetric extends DeterministicMetric {
  constructor(config = {}) {
    super({
      name: 'ast_similarity',
      supportedLanguages: ['javascript', 'python', 'java', 'cpp', 'go', 'rust'],
      ...config
    });
    
    this.parsers = new Map();
    this.initializeParsers();
  }

  /**
   * Get metric description
   * @returns {string} Metric description
   */
  getDescription() {
    return 'Measures structural similarity between code using Abstract Syntax Tree comparison';
  }

  /**
   * Get metric unit
   * @returns {string} Metric unit
   */
  getUnit() {
    return 'similarity_score';
  }

  /**
   * Get metric scale
   * @returns {string} Scale description
   */
  getScale() {
    return '0-1 (0=completely different, 1=identical structure)';
  }

  /**
   * Get applicable modes - only for PR_Recreate mode
   * @returns {Array<string>} Array of mode names
   */
  getApplicableModes() {
    return ['PR_Recreate'];
  }

  /**
   * Initialize tree-sitter parsers for supported languages
   */
  async initializeParsers() {
    try {
      // Note: In a real implementation, you'd need to install tree-sitter language packages
      // For now, we'll create placeholder parsers
      const languages = this.config.supportedLanguages;
      
      for (const lang of languages) {
        try {
          // This would normally load the actual tree-sitter language
          // const Language = require(`tree-sitter-${lang}`);
          // const parser = new Parser();
          // parser.setLanguage(Language);
          // this.parsers.set(lang, parser);
          
          // Placeholder for now
          this.parsers.set(lang, { language: lang, available: false });
        } catch (error) {
          this.log('warn', `Failed to load tree-sitter parser for ${lang}`, { 
            error: error.message 
          });
        }
      }
    } catch (error) {
      this.log('error', 'Failed to initialize AST parsers', { error: error.message });
    }
  }

  /**
   * Validate input data for AST similarity calculation
   * @param {Object} data - Data containing code to compare
   * @param {Object} context - Calculation context
   * @returns {boolean} True if data is valid
   */
  validateInput(data, context) {
    if (!super.validateInput(data, context)) {
      return false;
    }

    // Check for code comparison data
    const hasBeforeAfter = data.before !== undefined && data.after !== undefined;
    const hasOriginalModified = data.original !== undefined && data.modified !== undefined;
    const hasHumanAgent = data.humanCode !== undefined && data.agentCode !== undefined;
    const hasFiles = data.beforeFile !== undefined && data.afterFile !== undefined;

    return hasBeforeAfter || hasOriginalModified || hasHumanAgent || hasFiles;
  }

  /**
   * Calculate AST similarity between code samples
   * @param {Object} data - Code comparison data
   * @param {Object} context - Calculation context
   * @returns {Promise<number>} Similarity score (0-1)
   */
  async calculateValue(data, context) {
    try {
      let beforeCode, afterCode, language;

      // Extract code and language from various data formats
      if (data.before && data.after) {
        beforeCode = data.before;
        afterCode = data.after;
        language = data.language || this.detectLanguage(beforeCode, afterCode);
      } else if (data.original && data.modified) {
        beforeCode = data.original;
        afterCode = data.modified;
        language = data.language || this.detectLanguage(beforeCode, afterCode);
      } else if (data.humanCode && data.agentCode) {
        beforeCode = data.humanCode;
        afterCode = data.agentCode;
        language = data.language || this.detectLanguage(beforeCode, afterCode);
      } else if (data.beforeFile && data.afterFile) {
        const result = await this.loadFilesForComparison(data.beforeFile, data.afterFile);
        beforeCode = result.beforeCode;
        afterCode = result.afterCode;
        language = result.language;
      } else {
        throw new Error('No valid code comparison data found');
      }

      // Calculate similarity
      const similarity = await this.calculateSimilarity(beforeCode, afterCode, language, context);

      this.log('debug', 'AST similarity calculated', {
        similarity,
        language,
        beforeLength: beforeCode.length,
        afterLength: afterCode.length
      });

      return similarity;
    } catch (error) {
      this.log('error', 'AST similarity calculation failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Calculate similarity between two code samples
   * @param {string} beforeCode - Original code
   * @param {string} afterCode - Modified code
   * @param {string} language - Programming language
   * @param {Object} context - Calculation context
   * @returns {Promise<number>} Similarity score
   */
  async calculateSimilarity(beforeCode, afterCode, language, context) {
    // If codes are identical, return 1
    if (beforeCode === afterCode) {
      return 1.0;
    }

    // If either is empty, return 0
    if (!beforeCode.trim() || !afterCode.trim()) {
      return 0.0;
    }

    try {
      // Try AST-based comparison if parser is available
      if (this.parsers.has(language) && this.parsers.get(language).available) {
        return await this.calculateASTSimilarity(beforeCode, afterCode, language);
      } else {
        // Fallback to token-based similarity
        return this.calculateTokenSimilarity(beforeCode, afterCode, language);
      }
    } catch (error) {
      this.log('warn', 'AST parsing failed, falling back to token similarity', { 
        error: error.message 
      });
      return this.calculateTokenSimilarity(beforeCode, afterCode, language);
    }
  }

  /**
   * Calculate AST-based structural similarity
   * @param {string} beforeCode - Original code
   * @param {string} afterCode - Modified code
   * @param {string} language - Programming language
   * @returns {Promise<number>} Similarity score
   */
  async calculateASTSimilarity(beforeCode, afterCode, language) {
    // This is a placeholder implementation
    // In a real implementation, you would:
    // 1. Parse both code samples into ASTs
    // 2. Compare AST structures using tree edit distance or similar algorithms
    // 3. Calculate similarity based on structural differences
    
    const parser = this.parsers.get(language);
    
    try {
      // Parse both code samples
      const beforeAST = parser.parse(beforeCode);
      const afterAST = parser.parse(afterCode);
      
      // Calculate structural similarity
      const similarity = this.compareASTNodes(beforeAST.rootNode, afterAST.rootNode);
      
      return Math.max(0, Math.min(1, similarity));
    } catch (error) {
      throw new Error(`AST parsing failed: ${error.message}`);
    }
  }

  /**
   * Compare AST nodes recursively
   * @param {Object} node1 - First AST node
   * @param {Object} node2 - Second AST node
   * @returns {number} Similarity score
   */
  compareASTNodes(node1, node2) {
    // Placeholder implementation
    // Real implementation would do deep structural comparison
    
    if (!node1 || !node2) {
      return 0;
    }
    
    if (node1.type !== node2.type) {
      return 0.5; // Different node types but both exist
    }
    
    // Compare child nodes
    const children1 = node1.children || [];
    const children2 = node2.children || [];
    
    if (children1.length === 0 && children2.length === 0) {
      return 1; // Both are leaf nodes of same type
    }
    
    const maxChildren = Math.max(children1.length, children2.length);
    if (maxChildren === 0) {
      return 1;
    }
    
    let totalSimilarity = 0;
    for (let i = 0; i < maxChildren; i++) {
      const child1 = children1[i];
      const child2 = children2[i];
      
      if (child1 && child2) {
        totalSimilarity += this.compareASTNodes(child1, child2);
      } else {
        totalSimilarity += 0.3; // Penalty for missing child
      }
    }
    
    return totalSimilarity / maxChildren;
  }

  /**
   * Calculate token-based similarity as fallback
   * @param {string} beforeCode - Original code
   * @param {string} afterCode - Modified code
   * @param {string} language - Programming language
   * @returns {number} Similarity score
   */
  calculateTokenSimilarity(beforeCode, afterCode, language) {
    // Tokenize both code samples
    const beforeTokens = this.tokenizeCode(beforeCode, language);
    const afterTokens = this.tokenizeCode(afterCode, language);
    
    // Calculate Jaccard similarity
    const intersection = this.getTokenIntersection(beforeTokens, afterTokens);
    const union = this.getTokenUnion(beforeTokens, afterTokens);
    
    if (union.size === 0) {
      return 1; // Both empty
    }
    
    const jaccardSimilarity = intersection.size / union.size;
    
    // Also consider sequence similarity
    const sequenceSimilarity = this.calculateSequenceSimilarity(beforeTokens, afterTokens);
    
    // Combine both measures
    return (jaccardSimilarity * 0.6) + (sequenceSimilarity * 0.4);
  }

  /**
   * Tokenize code based on language
   * @param {string} code - Code to tokenize
   * @param {string} language - Programming language
   * @returns {Array<string>} Array of tokens
   */
  tokenizeCode(code, language) {
    // Basic tokenization - split on common delimiters
    const tokens = [];
    
    // Remove comments and strings for structural comparison
    const cleanCode = this.removeCommentsAndStrings(code, language);
    
    // Split on whitespace and common punctuation
    const rawTokens = cleanCode.split(/[\s\(\)\{\}\[\];,\.]+/);
    
    for (const token of rawTokens) {
      const trimmed = token.trim();
      if (trimmed.length > 0) {
        tokens.push(trimmed);
      }
    }
    
    return tokens;
  }

  /**
   * Remove comments and string literals for structural comparison
   * @param {string} code - Code to clean
   * @param {string} language - Programming language
   * @returns {string} Cleaned code
   */
  removeCommentsAndStrings(code, language) {
    // Basic implementation - remove common comment patterns
    let cleaned = code;
    
    // Remove single-line comments
    cleaned = cleaned.replace(/\/\/.*$/gm, '');
    cleaned = cleaned.replace(/#.*$/gm, '');
    
    // Remove multi-line comments
    cleaned = cleaned.replace(/\/\*[\s\S]*?\*\//g, '');
    
    // Remove string literals (basic)
    cleaned = cleaned.replace(/"[^"]*"/g, '""');
    cleaned = cleaned.replace(/'[^']*'/g, "''");
    cleaned = cleaned.replace(/`[^`]*`/g, '``');
    
    return cleaned;
  }

  /**
   * Get intersection of token sets
   * @param {Array<string>} tokens1 - First token array
   * @param {Array<string>} tokens2 - Second token array
   * @returns {Set<string>} Intersection set
   */
  getTokenIntersection(tokens1, tokens2) {
    const set1 = new Set(tokens1);
    const set2 = new Set(tokens2);
    const intersection = new Set();
    
    for (const token of set1) {
      if (set2.has(token)) {
        intersection.add(token);
      }
    }
    
    return intersection;
  }

  /**
   * Get union of token sets
   * @param {Array<string>} tokens1 - First token array
   * @param {Array<string>} tokens2 - Second token array
   * @returns {Set<string>} Union set
   */
  getTokenUnion(tokens1, tokens2) {
    const union = new Set([...tokens1, ...tokens2]);
    return union;
  }

  /**
   * Calculate sequence similarity using longest common subsequence
   * @param {Array<string>} tokens1 - First token array
   * @param {Array<string>} tokens2 - Second token array
   * @returns {number} Sequence similarity score
   */
  calculateSequenceSimilarity(tokens1, tokens2) {
    const lcs = this.longestCommonSubsequence(tokens1, tokens2);
    const maxLength = Math.max(tokens1.length, tokens2.length);
    
    if (maxLength === 0) {
      return 1;
    }
    
    return lcs / maxLength;
  }

  /**
   * Calculate longest common subsequence length
   * @param {Array<string>} seq1 - First sequence
   * @param {Array<string>} seq2 - Second sequence
   * @returns {number} LCS length
   */
  longestCommonSubsequence(seq1, seq2) {
    const m = seq1.length;
    const n = seq2.length;
    const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));
    
    for (let i = 1; i <= m; i++) {
      for (let j = 1; j <= n; j++) {
        if (seq1[i - 1] === seq2[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1] + 1;
        } else {
          dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
        }
      }
    }
    
    return dp[m][n];
  }

  /**
   * Detect programming language from code content
   * @param {string} code1 - First code sample
   * @param {string} code2 - Second code sample
   * @returns {string} Detected language
   */
  detectLanguage(code1, code2) {
    const combined = code1 + '\n' + code2;
    
    // Simple heuristics for language detection
    if (combined.includes('function') && combined.includes('{')) {
      return 'javascript';
    } else if (combined.includes('def ') && combined.includes(':')) {
      return 'python';
    } else if (combined.includes('public class') || combined.includes('import java')) {
      return 'java';
    } else if (combined.includes('#include') || combined.includes('std::')) {
      return 'cpp';
    } else if (combined.includes('package main') || combined.includes('func ')) {
      return 'go';
    } else if (combined.includes('fn ') && combined.includes('->')) {
      return 'rust';
    }
    
    return 'unknown';
  }

  /**
   * Load files for comparison
   * @param {string} beforeFile - Path to before file
   * @param {string} afterFile - Path to after file
   * @returns {Promise<Object>} File contents and language
   */
  async loadFilesForComparison(beforeFile, afterFile) {
    const { fileSystem } = require('../utils/FileSystem');
    
    const beforeCode = await fileSystem.readFile(beforeFile, 'utf8');
    const afterCode = await fileSystem.readFile(afterFile, 'utf8');
    
    // Detect language from file extension
    const extension = beforeFile.split('.').pop().toLowerCase();
    const language = this.getLanguageFromExtension(extension);
    
    return { beforeCode, afterCode, language };
  }

  /**
   * Get language from file extension
   * @param {string} extension - File extension
   * @returns {string} Language name
   */
  getLanguageFromExtension(extension) {
    const extensionMap = {
      'js': 'javascript',
      'ts': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'cc': 'cpp',
      'cxx': 'cpp',
      'c': 'c',
      'go': 'go',
      'rs': 'rust',
      'rb': 'ruby',
      'php': 'php',
      'cs': 'csharp'
    };
    
    return extensionMap[extension] || 'unknown';
  }

  /**
   * Higher similarity is better
   * @returns {boolean} True
   */
  isHigherBetter() {
    return true;
  }

  /**
   * Format similarity score for display
   * @param {number} value - Similarity score
   * @returns {string} Formatted value
   */
  formatValue(value) {
    if (value === null || value === undefined || value === 'NA') {
      return 'N/A';
    }

    const score = parseFloat(value);
    if (isNaN(score)) {
      return 'N/A';
    }

    const percentage = (score * 100).toFixed(1);
    return `${percentage}%`;
  }

  /**
   * Get similarity category
   * @param {number} similarity - Similarity score
   * @returns {string} Category description
   */
  getSimilarityCategory(similarity) {
    if (similarity >= 0.9) {
      return 'Very High';
    } else if (similarity >= 0.7) {
      return 'High';
    } else if (similarity >= 0.5) {
      return 'Medium';
    } else if (similarity >= 0.3) {
      return 'Low';
    } else {
      return 'Very Low';
    }
  }
}

module.exports = { ASTSimilarityMetric };
