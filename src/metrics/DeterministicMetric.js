const { BaseMetric } = require('./BaseMetric');

/**
 * Deterministic Metric - Base class for metrics that can be calculated directly
 * These metrics don't require LLM evaluation and produce consistent results
 */
class DeterministicMetric extends BaseMetric {
  constructor(config = {}) {
    super(config);
  }

  /**
   * Get metric type
   * @returns {string} Always returns 'deterministic'
   */
  getType() {
    return 'deterministic';
  }

  /**
   * Calculate metric synchronously (most deterministic metrics are fast)
   * @param {Object} data - Data to calculate metric from
   * @param {Object} context - Calculation context
   * @returns {Promise<number>} Metric value
   */
  async calculate(data, context) {
    try {
      if (!this.validateInput(data, context)) {
        throw new Error('Invalid input data for metric calculation');
      }

      const startTime = Date.now();
      const result = await this.calculateValue(data, context);
      const calculationTime = Date.now() - startTime;

      this.log('debug', 'Metric calculated', {
        value: result,
        calculationTime: `${calculationTime}ms`
      });

      return result;
    } catch (error) {
      this.log('error', 'Metric calculation failed', { error: error.message });
      return this.handleError(error, context);
    }
  }

  /**
   * Abstract method for actual value calculation
   * Must be implemented by concrete deterministic metric classes
   * 
   * @param {Object} data - Data to calculate metric from
   * @param {Object} context - Calculation context
   * @returns {Promise<number>} Calculated metric value
   */
  async calculateValue(data, context) {
    throw new Error(`calculateValue() method must be implemented by ${this.constructor.name}`);
  }

  /**
   * Validate that the result is a valid number
   * @param {any} result - Calculation result
   * @returns {boolean} True if result is valid
   */
  validateResult(result) {
    return typeof result === 'number' && !isNaN(result) && isFinite(result);
  }

  /**
   * Normalize result to ensure it's within expected bounds
   * @param {number} result - Raw calculation result
   * @returns {number} Normalized result
   */
  normalizeResult(result) {
    // Default implementation - no normalization
    // Override in subclasses if needed
    return result;
  }

  /**
   * Get default scale for deterministic metrics
   * @returns {string} Scale description
   */
  getScale() {
    return 'numeric';
  }

  /**
   * Format deterministic metric values
   * @param {number} value - Raw metric value
   * @returns {string} Formatted value
   */
  formatValue(value) {
    if (value === null || value === undefined || value === 'NA') {
      return 'N/A';
    }

    if (typeof value === 'number') {
      // For deterministic metrics, show appropriate precision
      if (Number.isInteger(value)) {
        return value.toString();
      } else {
        return Number(value.toFixed(3)).toString();
      }
    }

    return value.toString();
  }

  /**
   * Get statistical summary for multiple values
   * @param {Array<number>} values - Array of metric values
   * @returns {Object} Statistical summary
   */
  getStatistics(values) {
    const validValues = values.filter(v => 
      typeof v === 'number' && !isNaN(v) && isFinite(v)
    );

    if (validValues.length === 0) {
      return {
        count: 0,
        min: null,
        max: null,
        mean: null,
        median: null,
        stdDev: null
      };
    }

    const sorted = [...validValues].sort((a, b) => a - b);
    const count = validValues.length;
    const sum = validValues.reduce((acc, val) => acc + val, 0);
    const mean = sum / count;

    // Calculate median
    const median = count % 2 === 0
      ? (sorted[count / 2 - 1] + sorted[count / 2]) / 2
      : sorted[Math.floor(count / 2)];

    // Calculate standard deviation
    const variance = validValues.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / count;
    const stdDev = Math.sqrt(variance);

    return {
      count,
      min: sorted[0],
      max: sorted[count - 1],
      mean,
      median,
      stdDev
    };
  }

  /**
   * Compare two metric values
   * @param {number} value1 - First value
   * @param {number} value2 - Second value
   * @returns {number} -1 if value1 < value2, 0 if equal, 1 if value1 > value2
   */
  compare(value1, value2) {
    if (value1 === value2) return 0;
    return value1 < value2 ? -1 : 1;
  }

  /**
   * Check if a higher value is better for this metric
   * Override in subclasses where lower is better
   * @returns {boolean} True if higher values are better
   */
  isHigherBetter() {
    return true;
  }

  /**
   * Get the best value from an array of values
   * @param {Array<number>} values - Array of metric values
   * @returns {number} Best value
   */
  getBestValue(values) {
    const validValues = values.filter(v => 
      typeof v === 'number' && !isNaN(v) && isFinite(v)
    );

    if (validValues.length === 0) {
      return null;
    }

    return this.isHigherBetter()
      ? Math.max(...validValues)
      : Math.min(...validValues);
  }

  /**
   * Get the worst value from an array of values
   * @param {Array<number>} values - Array of metric values
   * @returns {number} Worst value
   */
  getWorstValue(values) {
    const validValues = values.filter(v => 
      typeof v === 'number' && !isNaN(v) && isFinite(v)
    );

    if (validValues.length === 0) {
      return null;
    }

    return this.isHigherBetter()
      ? Math.min(...validValues)
      : Math.max(...validValues);
  }
}

module.exports = { DeterministicMetric };
