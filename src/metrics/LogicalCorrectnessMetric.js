const { LLMAssessedMetric } = require('./LLMAssessedMetric');

/**
 * Logical Correctness Metric - Evaluates functional correctness and logical reasoning
 */
class LogicalCorrectnessMetric extends LLMAssessedMetric {
  constructor(config = {}) {
    super({
      name: 'functional_correctness',
      ...config
    });
  }

  /**
   * Get metric description
   * @returns {string} Metric description
   */
  getDescription() {
    return 'Evaluates functional correctness, logical reasoning, and whether the solution actually works as intended';
  }

  /**
   * Validate input data for logical correctness evaluation
   * @param {Object} data - Data containing solution to evaluate
   * @param {Object} context - Evaluation context
   * @returns {boolean} True if data is valid
   */
  validateInput(data, context) {
    if (!super.validateInput(data, context)) {
      return false;
    }

    const hasTaskSolution = data.task !== undefined && data.solution !== undefined;
    const hasProblemAnswer = data.problem !== undefined && data.answer !== undefined;
    const hasPromptResponse = data.prompt !== undefined && data.response !== undefined;
    const hasRequirementImplementation = data.requirements !== undefined && data.implementation !== undefined;

    return hasTaskSolution || hasProblemAnswer || hasPromptResponse || hasRequirementImplementation;
  }

  /**
   * Generate evaluation prompt for logical correctness assessment
   * @param {Object} data - Data containing solution to evaluate
   * @param {Object} context - Evaluation context
   * @returns {Promise<string>} Evaluation prompt
   */
  async generatePrompt(data, context) {
    const systemPrompt = this.buildSystemPrompt();
    
    let problem, solution;
    
    if (data.task && data.solution) {
      problem = data.task;
      solution = data.solution;
    } else if (data.problem && data.answer) {
      problem = data.problem;
      solution = data.answer;
    } else if (data.prompt && data.response) {
      problem = data.prompt;
      solution = data.response;
    } else if (data.requirements && data.implementation) {
      problem = data.requirements;
      solution = data.implementation;
    } else {
      throw new Error('No valid problem-solution pair found for evaluation');
    }

    const evaluationPrompt = `${systemPrompt}

PROBLEM/TASK:
${problem}

SOLUTION/IMPLEMENTATION:
${solution}

Please evaluate the logical and functional correctness of this solution. Consider:
1. Does the solution actually solve the stated problem?
2. Is the logic sound and reasoning correct?
3. Would this solution work correctly for the given inputs?
4. Are edge cases and boundary conditions handled properly?
5. Does the solution produce the expected outputs?
6. Is the algorithmic approach appropriate and correct?
7. Are there any logical flaws or incorrect assumptions?

Provide a score from 0-10 where:
- 10: Solution is completely correct and would work perfectly for all valid inputs
- 8-9: Solution is mostly correct with minor logical issues or edge case problems
- 6-7: Solution is generally correct but has some logical flaws or missing cases
- 4-5: Solution has the right idea but significant logical errors or gaps
- 2-3: Solution has major logical problems and would not work correctly
- 0-1: Solution is fundamentally flawed or completely incorrect

Score:`;

    return evaluationPrompt;
  }

  /**
   * Get evaluation criteria for logical correctness
   * @returns {Array<string>} Array of evaluation criteria
   */
  getEvaluationCriteria() {
    return [
      'Solution correctly addresses the stated problem',
      'Logic and reasoning are sound throughout',
      'Algorithm or approach is appropriate for the problem',
      'Edge cases and boundary conditions are handled',
      'Solution would produce correct outputs for valid inputs',
      'No logical contradictions or flawed assumptions',
      'Control flow and decision logic are correct',
      'Mathematical or computational logic is accurate'
    ];
  }

  /**
   * Get evaluation examples for logical correctness
   * @returns {Array<Object>} Array of examples with scores
   */
  getEvaluationExamples() {
    return [
      {
        score: 10,
        description: 'Solution is logically perfect, handles all cases correctly, and would work flawlessly'
      },
      {
        score: 8,
        description: 'Solution is mostly correct with sound logic, minor edge case issues or optimizations possible'
      },
      {
        score: 6,
        description: 'Solution works for main cases but has some logical gaps or incorrect handling of certain scenarios'
      },
      {
        score: 4,
        description: 'Solution has the right general approach but contains logical errors that would cause failures'
      },
      {
        score: 2,
        description: 'Solution has major logical flaws and would fail for many inputs or scenarios'
      },
      {
        score: 0,
        description: 'Solution is fundamentally incorrect or would not work at all'
      }
    ];
  }
}

module.exports = { LogicalCorrectnessMetric };
