const { DeterministicMetric } = require('./DeterministicMetric');
const { fileSystem } = require('../utils/FileSystem');
const { gitManager } = require('../utils/GitManager');

/**
 * Diff Metric - Measures file and line changes in code modifications
 */
class DiffMetric extends DeterministicMetric {
  constructor(config = {}) {
    super({
      name: 'diff_metrics',
      ...config
    });
  }

  /**
   * Get metric description
   * @returns {string} Metric description
   */
  getDescription() {
    return 'Measures file and line changes including additions, deletions, and modifications';
  }

  /**
   * Get metric unit
   * @returns {string} Metric unit
   */
  getUnit() {
    return 'count';
  }

  /**
   * Get metric scale
   * @returns {string} Scale description
   */
  getScale() {
    return 'numeric counts (0+)';
  }

  /**
   * Validate input data for diff calculation
   * @param {Object} data - Data containing diff information
   * @param {Object} context - Calculation context
   * @returns {boolean} True if data is valid
   */
  validateInput(data, context) {
    if (!super.validateInput(data, context)) {
      return false;
    }

    // Check for various diff data formats
    const hasGitDiff = data.gitDiff !== undefined;
    const hasFileChanges = data.fileChanges !== undefined;
    const hasDiffStats = data.diffStats !== undefined;
    const hasBeforeAfter = data.before !== undefined && data.after !== undefined;
    const hasWorkingDir = data.workingDirectory !== undefined;

    return hasGitDiff || hasFileChanges || hasDiffStats || hasBeforeAfter || hasWorkingDir;
  }

  /**
   * Calculate diff metrics from various data sources
   * @param {Object} data - Diff data
   * @param {Object} context - Calculation context
   * @returns {Promise<Object>} Diff metrics object
   */
  async calculateValue(data, context) {
    let diffMetrics = null;

    try {
      // Try different data sources in order of preference
      if (data.gitDiff) {
        diffMetrics = await this.calculateFromGitDiff(data.gitDiff, context);
      } else if (data.fileChanges) {
        diffMetrics = this.calculateFromFileChanges(data.fileChanges, context);
      } else if (data.diffStats) {
        diffMetrics = this.calculateFromDiffStats(data.diffStats, context);
      } else if (data.workingDirectory) {
        diffMetrics = await this.calculateFromWorkingDirectory(data.workingDirectory, context);
      } else if (data.before && data.after) {
        diffMetrics = await this.calculateFromBeforeAfter(data.before, data.after, context);
      } else {
        throw new Error('No valid diff data source found');
      }

      // Validate and normalize the result
      diffMetrics = this.normalizeDiffMetrics(diffMetrics);

      this.log('debug', 'Diff metrics calculated', diffMetrics);

      return diffMetrics;
    } catch (error) {
      this.log('error', 'Diff calculation failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Calculate diff metrics from git diff output
   * @param {string} gitDiff - Git diff output
   * @param {Object} context - Calculation context
   * @returns {Promise<Object>} Diff metrics
   */
  async calculateFromGitDiff(gitDiff, context) {
    const metrics = {
      filesChanged: 0,
      linesAdded: 0,
      linesDeleted: 0,
      linesModified: 0,
      totalLines: 0,
      fileTypes: {},
      largestFile: { name: '', lines: 0 }
    };

    const lines = gitDiff.split('\n');
    let currentFile = null;
    let currentFileLines = 0;

    for (const line of lines) {
      // File header
      if (line.startsWith('diff --git')) {
        if (currentFile) {
          this.updateLargestFile(metrics, currentFile, currentFileLines);
        }
        currentFile = this.extractFileName(line);
        currentFileLines = 0;
        metrics.filesChanged++;
        this.updateFileTypes(metrics, currentFile);
      }
      // Added lines
      else if (line.startsWith('+') && !line.startsWith('+++')) {
        metrics.linesAdded++;
        currentFileLines++;
      }
      // Deleted lines
      else if (line.startsWith('-') && !line.startsWith('---')) {
        metrics.linesDeleted++;
        currentFileLines++;
      }
    }

    // Handle last file
    if (currentFile) {
      this.updateLargestFile(metrics, currentFile, currentFileLines);
    }

    metrics.totalLines = metrics.linesAdded + metrics.linesDeleted;
    metrics.linesModified = Math.min(metrics.linesAdded, metrics.linesDeleted);

    return metrics;
  }

  /**
   * Calculate diff metrics from file changes array
   * @param {Array} fileChanges - Array of file change objects
   * @param {Object} context - Calculation context
   * @returns {Object} Diff metrics
   */
  calculateFromFileChanges(fileChanges, context) {
    const metrics = {
      filesChanged: fileChanges.length,
      linesAdded: 0,
      linesDeleted: 0,
      linesModified: 0,
      totalLines: 0,
      fileTypes: {},
      largestFile: { name: '', lines: 0 }
    };

    for (const change of fileChanges) {
      const added = change.linesAdded || change.additions || 0;
      const deleted = change.linesDeleted || change.deletions || 0;
      const total = added + deleted;

      metrics.linesAdded += added;
      metrics.linesDeleted += deleted;

      this.updateFileTypes(metrics, change.filename || change.file);
      this.updateLargestFile(metrics, change.filename || change.file, total);
    }

    metrics.totalLines = metrics.linesAdded + metrics.linesDeleted;
    metrics.linesModified = Math.min(metrics.linesAdded, metrics.linesDeleted);

    return metrics;
  }

  /**
   * Calculate diff metrics from pre-calculated diff stats
   * @param {Object} diffStats - Diff statistics object
   * @param {Object} context - Calculation context
   * @returns {Object} Diff metrics
   */
  calculateFromDiffStats(diffStats, context) {
    return {
      filesChanged: diffStats.filesChanged || diffStats.files || 0,
      linesAdded: diffStats.linesAdded || diffStats.additions || 0,
      linesDeleted: diffStats.linesDeleted || diffStats.deletions || 0,
      linesModified: diffStats.linesModified || 0,
      totalLines: diffStats.totalLines || 0,
      fileTypes: diffStats.fileTypes || {},
      largestFile: diffStats.largestFile || { name: '', lines: 0 }
    };
  }

  /**
   * Calculate diff metrics from working directory changes
   * @param {string} workingDirectory - Path to working directory
   * @param {Object} context - Calculation context
   * @returns {Promise<Object>} Diff metrics
   */
  async calculateFromWorkingDirectory(workingDirectory, context) {
    try {
      // Use git to get diff stats
      const diffOutput = await gitManager.getDiff(workingDirectory);
      return await this.calculateFromGitDiff(diffOutput, context);
    } catch (error) {
      this.log('warn', 'Failed to get git diff from working directory', { 
        error: error.message 
      });
      
      // Fallback: analyze file modifications directly
      return await this.analyzeDirectoryChanges(workingDirectory, context);
    }
  }

  /**
   * Calculate diff metrics by comparing before and after states
   * @param {string|Object} before - Before state (path or content)
   * @param {string|Object} after - After state (path or content)
   * @param {Object} context - Calculation context
   * @returns {Promise<Object>} Diff metrics
   */
  async calculateFromBeforeAfter(before, after, context) {
    // This is a simplified implementation
    // In a real scenario, you'd want to do proper file-by-file comparison
    const metrics = {
      filesChanged: 1,
      linesAdded: 0,
      linesDeleted: 0,
      linesModified: 0,
      totalLines: 0,
      fileTypes: {},
      largestFile: { name: 'comparison', lines: 0 }
    };

    if (typeof before === 'string' && typeof after === 'string') {
      const beforeLines = before.split('\n');
      const afterLines = after.split('\n');
      
      // Simple line-by-line comparison
      const maxLines = Math.max(beforeLines.length, afterLines.length);
      
      for (let i = 0; i < maxLines; i++) {
        const beforeLine = beforeLines[i] || '';
        const afterLine = afterLines[i] || '';
        
        if (i >= beforeLines.length) {
          metrics.linesAdded++;
        } else if (i >= afterLines.length) {
          metrics.linesDeleted++;
        } else if (beforeLine !== afterLine) {
          metrics.linesModified++;
        }
      }
      
      metrics.totalLines = metrics.linesAdded + metrics.linesDeleted + metrics.linesModified;
      metrics.largestFile.lines = metrics.totalLines;
    }

    return metrics;
  }

  /**
   * Analyze directory changes when git is not available
   * @param {string} directory - Directory path
   * @param {Object} context - Calculation context
   * @returns {Promise<Object>} Diff metrics
   */
  async analyzeDirectoryChanges(directory, context) {
    // This is a placeholder implementation
    // In practice, you'd need to compare against a baseline
    const metrics = {
      filesChanged: 0,
      linesAdded: 0,
      linesDeleted: 0,
      linesModified: 0,
      totalLines: 0,
      fileTypes: {},
      largestFile: { name: '', lines: 0 }
    };

    try {
      const files = await fileSystem.readdir(directory);
      metrics.filesChanged = files.length;
      
      // This is a very basic estimation
      // Real implementation would need proper baseline comparison
      for (const file of files) {
        const filePath = `${directory}/${file}`;
        const stats = await fileSystem.stat(filePath);
        
        if (stats.isFile()) {
          this.updateFileTypes(metrics, file);
          // Estimate lines based on file size (rough approximation)
          const estimatedLines = Math.ceil(stats.size / 50); // ~50 chars per line
          metrics.totalLines += estimatedLines;
          this.updateLargestFile(metrics, file, estimatedLines);
        }
      }
      
      // Rough estimation: assume 50% additions, 30% deletions, 20% modifications
      metrics.linesAdded = Math.ceil(metrics.totalLines * 0.5);
      metrics.linesDeleted = Math.ceil(metrics.totalLines * 0.3);
      metrics.linesModified = Math.ceil(metrics.totalLines * 0.2);
      
    } catch (error) {
      this.log('error', 'Failed to analyze directory changes', { error: error.message });
    }

    return metrics;
  }

  /**
   * Extract filename from git diff line
   * @param {string} diffLine - Git diff line
   * @returns {string} Filename
   */
  extractFileName(diffLine) {
    const match = diffLine.match(/diff --git a\/(.+) b\/(.+)/);
    return match ? match[2] : 'unknown';
  }

  /**
   * Update file types statistics
   * @param {Object} metrics - Metrics object
   * @param {string} filename - Filename
   */
  updateFileTypes(metrics, filename) {
    if (!filename) return;
    
    const extension = filename.split('.').pop().toLowerCase();
    if (!metrics.fileTypes[extension]) {
      metrics.fileTypes[extension] = 0;
    }
    metrics.fileTypes[extension]++;
  }

  /**
   * Update largest file tracking
   * @param {Object} metrics - Metrics object
   * @param {string} filename - Filename
   * @param {number} lines - Number of lines changed
   */
  updateLargestFile(metrics, filename, lines) {
    if (lines > metrics.largestFile.lines) {
      metrics.largestFile = { name: filename, lines };
    }
  }

  /**
   * Normalize and validate diff metrics
   * @param {Object} metrics - Raw metrics object
   * @returns {Object} Normalized metrics
   */
  normalizeDiffMetrics(metrics) {
    const normalized = {
      filesChanged: Math.max(0, metrics.filesChanged || 0),
      linesAdded: Math.max(0, metrics.linesAdded || 0),
      linesDeleted: Math.max(0, metrics.linesDeleted || 0),
      linesModified: Math.max(0, metrics.linesModified || 0),
      totalLines: Math.max(0, metrics.totalLines || 0),
      fileTypes: metrics.fileTypes || {},
      largestFile: metrics.largestFile || { name: '', lines: 0 }
    };

    // Ensure totalLines is consistent
    if (normalized.totalLines === 0) {
      normalized.totalLines = normalized.linesAdded + normalized.linesDeleted;
    }

    return normalized;
  }

  /**
   * Format diff metrics for display
   * @param {Object} value - Diff metrics object
   * @returns {string} Formatted value
   */
  formatValue(value) {
    if (value === null || value === undefined || value === 'NA') {
      return 'N/A';
    }

    if (typeof value === 'object') {
      const { filesChanged, linesAdded, linesDeleted, totalLines } = value;
      return `${filesChanged} files, +${linesAdded}/-${linesDeleted} lines (${totalLines} total)`;
    }

    return super.formatValue(value);
  }

  /**
   * Get change magnitude category
   * @param {Object} metrics - Diff metrics
   * @returns {string} Change magnitude
   */
  getChangeMagnitude(metrics) {
    const totalLines = metrics.totalLines || 0;
    
    if (totalLines === 0) {
      return 'No Changes';
    } else if (totalLines < 10) {
      return 'Minimal';
    } else if (totalLines < 50) {
      return 'Small';
    } else if (totalLines < 200) {
      return 'Medium';
    } else if (totalLines < 500) {
      return 'Large';
    } else {
      return 'Very Large';
    }
  }

  /**
   * Get change type analysis
   * @param {Object} metrics - Diff metrics
   * @returns {Object} Change type analysis
   */
  getChangeTypeAnalysis(metrics) {
    const { linesAdded, linesDeleted, linesModified, totalLines } = metrics;
    
    if (totalLines === 0) {
      return { type: 'none', description: 'No changes detected' };
    }

    const addedRatio = linesAdded / totalLines;
    const deletedRatio = linesDeleted / totalLines;
    
    if (addedRatio > 0.8) {
      return { type: 'addition', description: 'Primarily new code additions' };
    } else if (deletedRatio > 0.8) {
      return { type: 'deletion', description: 'Primarily code deletions' };
    } else if (Math.abs(addedRatio - deletedRatio) < 0.2) {
      return { type: 'modification', description: 'Balanced additions and deletions' };
    } else if (addedRatio > deletedRatio) {
      return { type: 'expansion', description: 'More additions than deletions' };
    } else {
      return { type: 'reduction', description: 'More deletions than additions' };
    }
  }

  /**
   * Higher total changes might indicate more comprehensive solutions
   * But this depends on context - sometimes minimal changes are better
   * @returns {boolean} True (assuming more comprehensive is better by default)
   */
  isHigherBetter() {
    return true;
  }
}

module.exports = { DiffMetric };
