const { DeterministicMetric } = require('./DeterministicMetric');

/**
 * Response Time Metric - Measures execution duration of agent commands
 */
class ResponseTimeMetric extends DeterministicMetric {
  constructor(config = {}) {
    super({
      name: 'response_time',
      ...config
    });
  }

  /**
   * Get metric description
   * @returns {string} Metric description
   */
  getDescription() {
    return 'Measures the execution time of agent commands in milliseconds';
  }

  /**
   * Get metric unit
   * @returns {string} Metric unit
   */
  getUnit() {
    return 'ms';
  }

  /**
   * Get metric scale
   * @returns {string} Scale description
   */
  getScale() {
    return 'milliseconds (0+)';
  }

  /**
   * Validate input data for response time calculation
   * @param {Object} data - Data containing timing information
   * @param {Object} context - Calculation context
   * @returns {boolean} True if data is valid
   */
  validateInput(data, context) {
    if (!super.validateInput(data, context)) {
      return false;
    }

    // Check for timing data in various formats
    const hasExecutionTime = data.executionTime !== undefined;
    const hasStartEnd = data.startTime !== undefined && data.endTime !== undefined;
    const hasDuration = data.duration !== undefined;
    const hasMetadata = data.metadata && (
      data.metadata.executionTime !== undefined ||
      data.metadata.duration !== undefined ||
      (data.metadata.startTime !== undefined && data.metadata.endTime !== undefined)
    );

    return hasExecutionTime || hasStartEnd || hasDuration || hasMetadata;
  }

  /**
   * Calculate response time from execution data
   * @param {Object} data - Execution data
   * @param {Object} context - Calculation context
   * @returns {Promise<number>} Response time in milliseconds
   */
  async calculateValue(data, context) {
    let responseTime = null;

    // Try different data formats
    if (data.executionTime !== undefined) {
      responseTime = data.executionTime;
    } else if (data.duration !== undefined) {
      responseTime = data.duration;
    } else if (data.startTime !== undefined && data.endTime !== undefined) {
      responseTime = data.endTime - data.startTime;
    } else if (data.metadata) {
      if (data.metadata.executionTime !== undefined) {
        responseTime = data.metadata.executionTime;
      } else if (data.metadata.duration !== undefined) {
        responseTime = data.metadata.duration;
      } else if (data.metadata.startTime !== undefined && data.metadata.endTime !== undefined) {
        responseTime = data.metadata.endTime - data.metadata.startTime;
      }
    }

    if (responseTime === null) {
      throw new Error('No valid timing data found in input');
    }

    // Convert to milliseconds if needed
    responseTime = this.normalizeToMilliseconds(responseTime, data);

    // Validate result
    if (!this.validateResult(responseTime)) {
      throw new Error(`Invalid response time calculated: ${responseTime}`);
    }

    // Ensure non-negative
    responseTime = Math.max(0, responseTime);

    this.log('debug', 'Response time calculated', {
      responseTime: `${responseTime}ms`,
      source: this.getTimingSource(data)
    });

    return responseTime;
  }

  /**
   * Normalize timing value to milliseconds
   * @param {number} value - Timing value
   * @param {Object} data - Original data for context
   * @returns {number} Value in milliseconds
   */
  normalizeToMilliseconds(value, data) {
    // If value is very large, it might be in microseconds or nanoseconds
    if (value > 1000000000) {
      // Likely nanoseconds (Node.js process.hrtime.bigint())
      return value / 1000000;
    } else if (value > 1000000) {
      // Likely microseconds
      return value / 1000;
    } else if (value < 1) {
      // Likely seconds
      return value * 1000;
    }
    
    // Assume milliseconds
    return value;
  }

  /**
   * Get the source of timing data for logging
   * @param {Object} data - Input data
   * @returns {string} Source description
   */
  getTimingSource(data) {
    if (data.executionTime !== undefined) return 'executionTime';
    if (data.duration !== undefined) return 'duration';
    if (data.startTime !== undefined && data.endTime !== undefined) return 'startTime/endTime';
    if (data.metadata?.executionTime !== undefined) return 'metadata.executionTime';
    if (data.metadata?.duration !== undefined) return 'metadata.duration';
    if (data.metadata?.startTime !== undefined && data.metadata?.endTime !== undefined) return 'metadata.startTime/endTime';
    return 'unknown';
  }

  /**
   * Lower response time is better
   * @returns {boolean} False (lower is better)
   */
  isHigherBetter() {
    return false;
  }

  /**
   * Format response time values with appropriate units
   * @param {number} value - Response time in milliseconds
   * @returns {string} Formatted value
   */
  formatValue(value) {
    if (value === null || value === undefined || value === 'NA') {
      return 'N/A';
    }

    const ms = parseFloat(value);
    if (isNaN(ms)) {
      return 'N/A';
    }

    // Format based on magnitude
    if (ms < 1000) {
      return `${Math.round(ms)}ms`;
    } else if (ms < 60000) {
      return `${(ms / 1000).toFixed(1)}s`;
    } else {
      const minutes = Math.floor(ms / 60000);
      const seconds = ((ms % 60000) / 1000).toFixed(1);
      return `${minutes}m ${seconds}s`;
    }
  }

  /**
   * Get performance category based on response time
   * @param {number} responseTime - Response time in milliseconds
   * @returns {string} Performance category
   */
  getPerformanceCategory(responseTime) {
    if (responseTime < 1000) {
      return 'Excellent';
    } else if (responseTime < 5000) {
      return 'Good';
    } else if (responseTime < 15000) {
      return 'Average';
    } else if (responseTime < 30000) {
      return 'Slow';
    } else {
      return 'Very Slow';
    }
  }

  /**
   * Get statistical analysis for response times
   * @param {Array<number>} values - Array of response time values
   * @returns {Object} Enhanced statistical summary
   */
  getStatistics(values) {
    const baseStats = super.getStatistics(values);
    
    if (baseStats.count === 0) {
      return baseStats;
    }

    // Add performance categorization
    const validValues = values.filter(v => 
      typeof v === 'number' && !isNaN(v) && isFinite(v)
    );

    const categories = {
      'Excellent': 0,
      'Good': 0,
      'Average': 0,
      'Slow': 0,
      'Very Slow': 0
    };

    validValues.forEach(value => {
      const category = this.getPerformanceCategory(value);
      categories[category]++;
    });

    // Calculate percentiles
    const sorted = [...validValues].sort((a, b) => a - b);
    const p50 = this.getPercentile(sorted, 50);
    const p90 = this.getPercentile(sorted, 90);
    const p95 = this.getPercentile(sorted, 95);
    const p99 = this.getPercentile(sorted, 99);

    return {
      ...baseStats,
      categories,
      percentiles: {
        p50,
        p90,
        p95,
        p99
      },
      formatted: {
        min: this.formatValue(baseStats.min),
        max: this.formatValue(baseStats.max),
        mean: this.formatValue(baseStats.mean),
        median: this.formatValue(baseStats.median),
        p90: this.formatValue(p90),
        p95: this.formatValue(p95),
        p99: this.formatValue(p99)
      }
    };
  }

  /**
   * Calculate percentile value
   * @param {Array<number>} sortedValues - Sorted array of values
   * @param {number} percentile - Percentile to calculate (0-100)
   * @returns {number} Percentile value
   */
  getPercentile(sortedValues, percentile) {
    if (sortedValues.length === 0) {
      return null;
    }

    const index = (percentile / 100) * (sortedValues.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    
    if (lower === upper) {
      return sortedValues[lower];
    }
    
    const weight = index - lower;
    return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
  }

  /**
   * Detect potential performance issues
   * @param {Array<number>} values - Array of response time values
   * @returns {Array<string>} Array of detected issues
   */
  detectPerformanceIssues(values) {
    const issues = [];
    const stats = this.getStatistics(values);
    
    if (stats.count === 0) {
      return issues;
    }

    // Check for consistently slow performance
    if (stats.mean > 30000) {
      issues.push('Average response time is very slow (>30s)');
    } else if (stats.mean > 15000) {
      issues.push('Average response time is slow (>15s)');
    }

    // Check for high variability
    if (stats.stdDev > stats.mean) {
      issues.push('High variability in response times (high standard deviation)');
    }

    // Check for outliers
    if (stats.max > stats.mean + (3 * stats.stdDev)) {
      issues.push('Potential outliers detected (values >3 standard deviations from mean)');
    }

    // Check P95 vs P50 ratio
    if (stats.percentiles.p95 > stats.percentiles.p50 * 3) {
      issues.push('Significant performance degradation in worst-case scenarios (P95 >> P50)');
    }

    return issues;
  }
}

module.exports = { ResponseTimeMetric };
