/**
 * Base Metric - Abstract class for all metrics
 * Defines the standardized interface for metric implementations
 */
class BaseMetric {
  constructor(config = {}) {
    this.config = {
      name: this.constructor.name.replace('Metric', '').toLowerCase(),
      ...config
    };
    this.name = this.config.name;
  }

  /**
   * Abstract method to calculate the metric
   * Must be implemented by concrete metric classes
   * 
   * @param {Object} data - Data to calculate metric from
   * @param {Object} context - Calculation context
   * @returns {Promise<number|string>} Metric value
   */
  async calculate(data, context) {
    throw new Error(`calculate() method must be implemented by ${this.constructor.name}`);
  }

  /**
   * Validate input data for metric calculation
   * @param {Object} data - Data to validate
   * @param {Object} context - Validation context
   * @returns {boolean} True if data is valid
   */
  validateInput(data, context) {
    return data !== null && data !== undefined;
  }

  /**
   * Get metric metadata
   * @returns {Object} Metric metadata
   */
  getMetadata() {
    return {
      name: this.name,
      type: this.getType(),
      description: this.getDescription(),
      unit: this.getUnit(),
      scale: this.getScale(),
      applicableModes: this.getApplicableModes()
    };
  }

  /**
   * Get metric type (deterministic or llm_assessed)
   * @returns {string} Metric type
   */
  getType() {
    throw new Error(`getType() method must be implemented by ${this.constructor.name}`);
  }

  /**
   * Get metric description
   * @returns {string} Metric description
   */
  getDescription() {
    throw new Error(`getDescription() method must be implemented by ${this.constructor.name}`);
  }

  /**
   * Get metric unit
   * @returns {string} Metric unit
   */
  getUnit() {
    return '';
  }

  /**
   * Get metric scale (e.g., "0-10", "seconds", "count")
   * @returns {string} Metric scale
   */
  getScale() {
    return '';
  }

  /**
   * Get applicable modes for this metric
   * @returns {Array<string>} Array of mode names
   */
  getApplicableModes() {
    return ['LLM_Evaluator', 'PR_Recreate'];
  }

  /**
   * Check if metric is applicable for given mode
   * @param {string} mode - Mode name
   * @returns {boolean} True if applicable
   */
  isApplicableForMode(mode) {
    return this.getApplicableModes().includes(mode);
  }

  /**
   * Format metric value for display
   * @param {number|string} value - Raw metric value
   * @returns {string} Formatted value
   */
  formatValue(value) {
    if (value === null || value === undefined || value === 'NA') {
      return 'N/A';
    }

    if (typeof value === 'number') {
      // Round to 2 decimal places for display
      return Number(value.toFixed(2)).toString();
    }

    return value.toString();
  }

  /**
   * Handle calculation errors gracefully
   * @param {Error} error - The error that occurred
   * @param {Object} context - Calculation context
   * @returns {string} Error result (typically 'NA')
   */
  handleError(error, context) {
    console.warn(`Metric ${this.name} calculation failed:`, error.message);
    return 'NA';
  }

  /**
   * Log metric calculation details
   * @param {string} level - Log level
   * @param {string} message - Log message
   * @param {Object} data - Additional data
   */
  log(level, message, data = {}) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${level.toUpperCase()}] [${this.name}] ${message}`, 
                data && Object.keys(data).length > 0 ? data : '');
  }

  /**
   * Get configuration
   * @returns {Object} Current configuration
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * Update configuration
   * @param {Object} newConfig - New configuration options
   */
  updateConfig(newConfig) {
    this.config = {
      ...this.config,
      ...newConfig
    };
  }
}

module.exports = { BaseMetric };
