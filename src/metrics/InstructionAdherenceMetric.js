const { LLMAssessedMetric } = require('./LLMAssessedMetric');

/**
 * Instruction Adherence Metric - Evaluates how well the agent followed specific instructions
 */
class InstructionAdherenceMetric extends LLMAssessedMetric {
  constructor(config = {}) {
    super({
      name: 'instruction_adherence',
      ...config
    });
  }

  /**
   * Get metric description
   * @returns {string} Metric description
   */
  getDescription() {
    return 'Evaluates how well the agent followed specific instructions, constraints, and requirements';
  }

  /**
   * Validate input data for instruction adherence evaluation
   * @param {Object} data - Data containing instructions and response
   * @param {Object} context - Evaluation context
   * @returns {boolean} True if data is valid
   */
  validateInput(data, context) {
    if (!super.validateInput(data, context)) {
      return false;
    }

    const hasInstructionResponse = data.instruction !== undefined && data.response !== undefined;
    const hasPromptOutput = data.prompt !== undefined && data.output !== undefined;
    const hasRequirementsImplementation = data.requirements !== undefined && data.implementation !== undefined;
    const hasGuidelinesResult = data.guidelines !== undefined && data.result !== undefined;

    return hasInstructionResponse || hasPromptOutput || hasRequirementsImplementation || hasGuidelinesResult;
  }

  /**
   * Generate evaluation prompt for instruction adherence assessment
   * @param {Object} data - Data containing instructions and response
   * @param {Object} context - Evaluation context
   * @returns {Promise<string>} Evaluation prompt
   */
  async generatePrompt(data, context) {
    const systemPrompt = this.buildSystemPrompt();
    
    let instructions, response;
    
    if (data.instruction && data.response) {
      instructions = data.instruction;
      response = data.response;
    } else if (data.prompt && data.output) {
      instructions = data.prompt;
      response = data.output;
    } else if (data.requirements && data.implementation) {
      instructions = data.requirements;
      response = data.implementation;
    } else if (data.guidelines && data.result) {
      instructions = data.guidelines;
      response = data.result;
    } else {
      throw new Error('No valid instruction-response pair found for evaluation');
    }

    const evaluationPrompt = `${systemPrompt}

INSTRUCTIONS/REQUIREMENTS:
${instructions}

AGENT RESPONSE:
${response}

Please evaluate how well the agent followed the given instructions. Consider:
1. Did the agent follow all explicit instructions and requirements?
2. Were any constraints or limitations properly respected?
3. Did the agent adhere to specified formats, styles, or conventions?
4. Were any "do not" or prohibition instructions followed?
5. Did the agent stay within the specified scope and boundaries?
6. Were any specific methodologies or approaches used as requested?
7. Did the agent follow any sequencing or ordering requirements?

Provide a score from 0-10 where:
- 10: Perfect adherence to all instructions and requirements
- 8-9: Excellent adherence with only minor deviations
- 6-7: Good adherence but missed some instructions or requirements
- 4-5: Partial adherence with several missed or ignored instructions
- 2-3: Poor adherence with many instructions not followed
- 0-1: Failed to follow instructions or completely ignored requirements

Score:`;

    return evaluationPrompt;
  }

  /**
   * Get evaluation criteria for instruction adherence
   * @returns {Array<string>} Array of evaluation criteria
   */
  getEvaluationCriteria() {
    return [
      'All explicit instructions are followed',
      'Constraints and limitations are respected',
      'Specified formats and conventions are used',
      'Prohibition instructions are obeyed',
      'Response stays within specified scope',
      'Required methodologies or approaches are used',
      'Sequencing and ordering requirements are followed',
      'No unauthorized deviations from instructions'
    ];
  }

  /**
   * Get evaluation examples for instruction adherence
   * @returns {Array<Object>} Array of examples with scores
   */
  getEvaluationExamples() {
    return [
      {
        score: 10,
        description: 'Agent follows every instruction perfectly, respects all constraints, and adheres to all specified requirements'
      },
      {
        score: 8,
        description: 'Agent follows almost all instructions with only very minor deviations that don\'t affect the outcome'
      },
      {
        score: 6,
        description: 'Agent follows most instructions but misses some requirements or makes minor unauthorized changes'
      },
      {
        score: 4,
        description: 'Agent follows some instructions but ignores several important requirements or constraints'
      },
      {
        score: 2,
        description: 'Agent follows few instructions and makes many unauthorized deviations or ignores constraints'
      },
      {
        score: 0,
        description: 'Agent completely ignores instructions or does something entirely different than requested'
      }
    ];
  }

  /**
   * Analyze instruction adherence patterns
   * @param {Object} data - Evaluation data
   * @returns {Object} Adherence analysis
   */
  analyzeInstructionAdherence(data) {
    const analysis = {
      explicitInstructions: [],
      constraints: [],
      formatRequirements: [],
      prohibitions: [],
      adherenceIndicators: [],
      violations: []
    };

    let instructions, response;
    
    if (data.instruction && data.response) {
      instructions = data.instruction;
      response = data.response;
    } else if (data.prompt && data.output) {
      instructions = data.prompt;
      response = data.output;
    } else if (data.requirements && data.implementation) {
      instructions = data.requirements;
      response = data.implementation;
    } else if (data.guidelines && data.result) {
      instructions = data.guidelines;
      response = data.result;
    }

    if (instructions && response) {
      analysis.explicitInstructions = this.extractExplicitInstructions(instructions);
      analysis.constraints = this.extractConstraints(instructions);
      analysis.formatRequirements = this.extractFormatRequirements(instructions);
      analysis.prohibitions = this.extractProhibitions(instructions);
      analysis.adherenceIndicators = this.findAdherenceIndicators(instructions, response);
      analysis.violations = this.findViolations(instructions, response);
    }

    return analysis;
  }

  /**
   * Extract explicit instructions from the instruction text
   * @param {string} instructions - Instruction text
   * @returns {Array<string>} Explicit instructions
   */
  extractExplicitInstructions(instructions) {
    const instructionPatterns = [
      /please\s+([^.!?]+)/gi,
      /you\s+should\s+([^.!?]+)/gi,
      /make\s+sure\s+([^.!?]+)/gi,
      /ensure\s+([^.!?]+)/gi,
      /implement\s+([^.!?]+)/gi,
      /create\s+([^.!?]+)/gi,
      /write\s+([^.!?]+)/gi,
      /use\s+([^.!?]+)/gi
    ];

    const instructions_found = [];
    for (const pattern of instructionPatterns) {
      const matches = instructions.matchAll(pattern);
      for (const match of matches) {
        instructions_found.push(match[1].trim());
      }
    }

    return instructions_found;
  }

  /**
   * Extract constraints from the instruction text
   * @param {string} instructions - Instruction text
   * @returns {Array<string>} Constraints
   */
  extractConstraints(instructions) {
    const constraintPatterns = [
      /must\s+([^.!?]+)/gi,
      /cannot\s+([^.!?]+)/gi,
      /should\s+not\s+([^.!?]+)/gi,
      /limit\s+([^.!?]+)/gi,
      /maximum\s+([^.!?]+)/gi,
      /minimum\s+([^.!?]+)/gi,
      /only\s+([^.!?]+)/gi
    ];

    const constraints = [];
    for (const pattern of constraintPatterns) {
      const matches = instructions.matchAll(pattern);
      for (const match of matches) {
        constraints.push(match[0].trim());
      }
    }

    return constraints;
  }

  /**
   * Extract format requirements from the instruction text
   * @param {string} instructions - Instruction text
   * @returns {Array<string>} Format requirements
   */
  extractFormatRequirements(instructions) {
    const formatPatterns = [
      /format\s+([^.!?]+)/gi,
      /style\s+([^.!?]+)/gi,
      /structure\s+([^.!?]+)/gi,
      /json/gi,
      /xml/gi,
      /markdown/gi,
      /csv/gi
    ];

    const formats = [];
    for (const pattern of formatPatterns) {
      const matches = instructions.matchAll(pattern);
      for (const match of matches) {
        formats.push(match[0].trim());
      }
    }

    return formats;
  }

  /**
   * Extract prohibitions from the instruction text
   * @param {string} instructions - Instruction text
   * @returns {Array<string>} Prohibitions
   */
  extractProhibitions(instructions) {
    const prohibitionPatterns = [
      /do\s+not\s+([^.!?]+)/gi,
      /don't\s+([^.!?]+)/gi,
      /avoid\s+([^.!?]+)/gi,
      /never\s+([^.!?]+)/gi,
      /prohibited\s+([^.!?]+)/gi,
      /forbidden\s+([^.!?]+)/gi
    ];

    const prohibitions = [];
    for (const pattern of prohibitionPatterns) {
      const matches = instructions.matchAll(pattern);
      for (const match of matches) {
        prohibitions.push(match[0].trim());
      }
    }

    return prohibitions;
  }

  /**
   * Find indicators of instruction adherence in the response
   * @param {string} instructions - Original instructions
   * @param {string} response - Agent response
   * @returns {Array<string>} Adherence indicators
   */
  findAdherenceIndicators(instructions, response) {
    const indicators = [];
    
    // Check for format adherence
    if (instructions.toLowerCase().includes('json') && response.includes('{')) {
      indicators.push('Follows JSON format requirement');
    }
    
    if (instructions.toLowerCase().includes('markdown') && response.includes('#')) {
      indicators.push('Follows Markdown format requirement');
    }
    
    // Check for specific keyword adherence
    const instructionWords = instructions.toLowerCase().split(/\s+/);
    const responseWords = response.toLowerCase().split(/\s+/);
    
    const keyTerms = instructionWords.filter(word => 
      word.length > 4 && 
      !['should', 'would', 'could', 'please', 'make', 'sure'].includes(word)
    );
    
    for (const term of keyTerms) {
      if (responseWords.includes(term)) {
        indicators.push(`Uses required term: ${term}`);
      }
    }

    return indicators;
  }

  /**
   * Find potential violations of instructions
   * @param {string} instructions - Original instructions
   * @param {string} response - Agent response
   * @returns {Array<string>} Potential violations
   */
  findViolations(instructions, response) {
    const violations = [];
    
    // Check for prohibition violations
    if (instructions.toLowerCase().includes('do not') || instructions.toLowerCase().includes("don't")) {
      // This would need more sophisticated analysis in a real implementation
      // For now, just flag if response is very different from instructions
      if (response.length < instructions.length * 0.1) {
        violations.push('Response may be too brief given instructions');
      }
    }
    
    return violations;
  }

  /**
   * Get adherence category based on score
   * @param {number} score - Adherence score (0-10)
   * @returns {string} Category description
   */
  getAdherenceCategory(score) {
    if (score >= 9) {
      return 'Excellent Adherence';
    } else if (score >= 7) {
      return 'Good Adherence';
    } else if (score >= 5) {
      return 'Partial Adherence';
    } else if (score >= 3) {
      return 'Poor Adherence';
    } else {
      return 'Non-Adherent';
    }
  }
}

module.exports = { InstructionAdherenceMetric };
