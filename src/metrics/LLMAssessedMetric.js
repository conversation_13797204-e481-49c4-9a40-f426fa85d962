const { BaseMetric } = require('./BaseMetric');

/**
 * LLM Assessed Metric - Base class for metrics that require LLM evaluation
 * These metrics use AI to assess qualitative aspects of code/output
 */
class LLMAssessedMetric extends BaseMetric {
  constructor(config = {}) {
    super({
      llmProvider: 'openai',
      llmModel: 'gpt-4',
      maxRetries: 3,
      retryDelay: 1000,
      ...config
    });
  }

  /**
   * Get metric type
   * @returns {string} Always returns 'llm_assessed'
   */
  getType() {
    return 'llm_assessed';
  }

  /**
   * Get default scale for LLM assessed metrics
   * @returns {string} Scale description
   */
  getScale() {
    return '0-10';
  }

  /**
   * Calculate metric using LLM evaluation
   * @param {Object} data - Data to evaluate
   * @param {Object} context - Evaluation context
   * @returns {Promise<number>} Metric score (0-10)
   */
  async calculate(data, context) {
    try {
      if (!this.validateInput(data, context)) {
        throw new Error('Invalid input data for LLM metric evaluation');
      }

      const startTime = Date.now();
      
      // Generate evaluation prompt
      const prompt = await this.generatePrompt(data, context);
      
      // Get LLM evaluation with retries
      const score = await this.evaluateWithLLM(prompt, context);
      
      // Validate and normalize score
      const normalizedScore = this.normalizeScore(score);
      
      const evaluationTime = Date.now() - startTime;

      this.log('debug', 'LLM metric evaluated', {
        score: normalizedScore,
        evaluationTime: `${evaluationTime}ms`
      });

      return normalizedScore;
    } catch (error) {
      this.log('error', 'LLM metric evaluation failed', { error: error.message });
      return this.handleError(error, context);
    }
  }

  /**
   * Generate evaluation prompt for the LLM
   * Must be implemented by concrete LLM metric classes
   * 
   * @param {Object} data - Data to evaluate
   * @param {Object} context - Evaluation context
   * @returns {Promise<string>} Evaluation prompt
   */
  async generatePrompt(data, context) {
    throw new Error(`generatePrompt() method must be implemented by ${this.constructor.name}`);
  }

  /**
   * Evaluate using LLM with retry logic
   * @param {string} prompt - Evaluation prompt
   * @param {Object} context - Evaluation context
   * @returns {Promise<number>} LLM score
   */
  async evaluateWithLLM(prompt, context) {
    let lastError;
    
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        this.log('debug', `LLM evaluation attempt ${attempt}`, { 
          prompt: prompt.substring(0, 100) + '...' 
        });
        
        const score = await this.callLLM(prompt, context);
        
        if (this.isValidScore(score)) {
          return score;
        } else {
          throw new Error(`Invalid score returned: ${score}`);
        }
      } catch (error) {
        lastError = error;
        this.log('warn', `LLM evaluation attempt ${attempt} failed`, { 
          error: error.message 
        });
        
        if (attempt < this.config.maxRetries) {
          await this.delay(this.config.retryDelay * attempt);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Make actual LLM API call
   * @param {string} prompt - Evaluation prompt
   * @param {Object} context - Evaluation context
   * @returns {Promise<number>} Raw LLM score
   */
  async callLLM(prompt, context) {
    // This will be implemented using the PromptGenerator utility
    // For now, return a placeholder
    throw new Error('LLM integration not yet implemented. Please complete Task 10.');
  }

  /**
   * Validate that the LLM returned a valid score
   * @param {any} score - Score returned by LLM
   * @returns {boolean} True if score is valid
   */
  isValidScore(score) {
    const numScore = parseFloat(score);
    return !isNaN(numScore) && numScore >= 0 && numScore <= 10;
  }

  /**
   * Normalize score to 0-10 range
   * @param {number} score - Raw score
   * @returns {number} Normalized score
   */
  normalizeScore(score) {
    const numScore = parseFloat(score);
    
    if (isNaN(numScore)) {
      return 0;
    }
    
    // Clamp to 0-10 range
    return Math.max(0, Math.min(10, numScore));
  }

  /**
   * Format LLM assessed metric values
   * @param {number} value - Raw metric value
   * @returns {string} Formatted value
   */
  formatValue(value) {
    if (value === null || value === undefined || value === 'NA') {
      return 'N/A';
    }

    if (typeof value === 'number') {
      return Number(value.toFixed(1)).toString();
    }

    return value.toString();
  }

  /**
   * Get evaluation criteria for this metric
   * Should be implemented by concrete classes
   * @returns {Array<string>} Array of evaluation criteria
   */
  getEvaluationCriteria() {
    return [];
  }

  /**
   * Get examples for this metric evaluation
   * Should be implemented by concrete classes
   * @returns {Array<Object>} Array of examples with scores
   */
  getEvaluationExamples() {
    return [];
  }

  /**
   * Build system prompt for LLM evaluation
   * @returns {string} System prompt
   */
  buildSystemPrompt() {
    const criteria = this.getEvaluationCriteria();
    const examples = this.getEvaluationExamples();
    
    let systemPrompt = `You are an expert code reviewer evaluating ${this.getDescription()}.

Evaluation Criteria:
${criteria.map(criterion => `- ${criterion}`).join('\n')}

Scoring Scale: 0-10 (where 10 is excellent, 0 is very poor)
- 9-10: Excellent
- 7-8: Good
- 5-6: Average
- 3-4: Below Average
- 1-2: Poor
- 0: Very Poor

`;

    if (examples.length > 0) {
      systemPrompt += `Examples:\n`;
      examples.forEach((example, index) => {
        systemPrompt += `Example ${index + 1} (Score: ${example.score}): ${example.description}\n`;
      });
      systemPrompt += '\n';
    }

    systemPrompt += `Instructions:
1. Analyze the provided code/output carefully
2. Consider all evaluation criteria
3. Provide a single numeric score from 0-10
4. Be consistent and objective in your evaluation

Respond with only the numeric score (e.g., "7.5").`;

    return systemPrompt;
  }

  /**
   * Delay execution for retry logic
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise<void>}
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Check if higher scores are better (always true for LLM metrics)
   * @returns {boolean} Always true
   */
  isHigherBetter() {
    return true;
  }

  /**
   * Get confidence level for LLM evaluation
   * @param {Array<number>} scores - Multiple scores for same evaluation
   * @returns {number} Confidence level (0-1)
   */
  getConfidenceLevel(scores) {
    if (scores.length < 2) {
      return 0.5; // Low confidence with single evaluation
    }

    const validScores = scores.filter(s => typeof s === 'number' && !isNaN(s));
    if (validScores.length === 0) {
      return 0;
    }

    // Calculate standard deviation
    const mean = validScores.reduce((sum, score) => sum + score, 0) / validScores.length;
    const variance = validScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / validScores.length;
    const stdDev = Math.sqrt(variance);

    // Lower standard deviation = higher confidence
    // Normalize to 0-1 range (assuming max stdDev of 3 for 0-10 scale)
    const confidence = Math.max(0, 1 - (stdDev / 3));
    
    return confidence;
  }

  /**
   * Get consensus score from multiple evaluations
   * @param {Array<number>} scores - Multiple scores
   * @returns {number} Consensus score
   */
  getConsensusScore(scores) {
    const validScores = scores.filter(s => typeof s === 'number' && !isNaN(s));
    
    if (validScores.length === 0) {
      return null;
    }

    // Use median for consensus to reduce impact of outliers
    const sorted = [...validScores].sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    
    if (sorted.length % 2 === 0) {
      return (sorted[middle - 1] + sorted[middle]) / 2;
    } else {
      return sorted[middle];
    }
  }
}

module.exports = { LLMAssessedMetric };
