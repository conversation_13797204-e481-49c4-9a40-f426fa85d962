const { LLMAssessedMetric } = require('./LLMAssessedMetric');

/**
 * Completeness Metric - Evaluates how completely the agent addressed the given task
 */
class CompletenessMetric extends LLMAssessedMetric {
  constructor(config = {}) {
    super({
      name: 'completeness',
      ...config
    });
  }

  /**
   * Get metric description
   * @returns {string} Metric description
   */
  getDescription() {
    return 'Evaluates how completely the agent addressed all aspects of the given task or prompt';
  }

  /**
   * Validate input data for completeness evaluation
   * @param {Object} data - Data containing task and response
   * @param {Object} context - Evaluation context
   * @returns {boolean} True if data is valid
   */
  validateInput(data, context) {
    if (!super.validateInput(data, context)) {
      return false;
    }

    // Check for required evaluation data
    const hasPromptResponse = data.prompt !== undefined && data.response !== undefined;
    const hasTaskOutput = data.task !== undefined && data.output !== undefined;
    const hasInstructionResult = data.instruction !== undefined && data.result !== undefined;

    return hasPromptResponse || hasTaskOutput || hasInstructionResult;
  }

  /**
   * Generate evaluation prompt for completeness assessment
   * @param {Object} data - Data containing task and response
   * @param {Object} context - Evaluation context
   * @returns {Promise<string>} Evaluation prompt
   */
  async generatePrompt(data, context) {
    const systemPrompt = this.buildSystemPrompt();
    
    let task, response;
    
    // Extract task and response from various data formats
    if (data.prompt && data.response) {
      task = data.prompt;
      response = data.response;
    } else if (data.task && data.output) {
      task = data.task;
      response = data.output;
    } else if (data.instruction && data.result) {
      task = data.instruction;
      response = data.result;
    } else {
      throw new Error('No valid task-response pair found in data');
    }

    const evaluationPrompt = `${systemPrompt}

TASK/PROMPT:
${task}

AGENT RESPONSE:
${response}

Please evaluate how completely the agent addressed the given task. Consider:
1. Did the agent address all parts of the task?
2. Are all requirements fulfilled?
3. Are there any missing components or incomplete implementations?
4. Does the response cover the full scope of what was requested?

Provide a score from 0-10 where:
- 10: Completely addresses every aspect of the task
- 8-9: Addresses most aspects with minor omissions
- 6-7: Addresses main aspects but missing some important parts
- 4-5: Partially addresses the task with significant gaps
- 2-3: Minimal addressing of the task requirements
- 0-1: Fails to address the task meaningfully

Score:`;

    return evaluationPrompt;
  }

  /**
   * Get evaluation criteria for completeness
   * @returns {Array<string>} Array of evaluation criteria
   */
  getEvaluationCriteria() {
    return [
      'All parts of the task/prompt are addressed',
      'All explicit requirements are fulfilled',
      'All implicit requirements are considered',
      'No significant components are missing',
      'The scope of the response matches the scope of the request',
      'Edge cases and special conditions are handled',
      'The solution is comprehensive and thorough'
    ];
  }

  /**
   * Get evaluation examples for completeness
   * @returns {Array<Object>} Array of examples with scores
   */
  getEvaluationExamples() {
    return [
      {
        score: 10,
        description: 'Agent implements all requested features, handles all edge cases, includes proper error handling, documentation, and tests as requested'
      },
      {
        score: 8,
        description: 'Agent implements all main features and most requirements, with only minor details missing (e.g., some edge cases not handled)'
      },
      {
        score: 6,
        description: 'Agent implements core functionality but misses some important requirements (e.g., missing error handling or incomplete feature set)'
      },
      {
        score: 4,
        description: 'Agent provides partial implementation that addresses some but not all major requirements'
      },
      {
        score: 2,
        description: 'Agent provides minimal implementation that only touches on a few aspects of the task'
      },
      {
        score: 0,
        description: 'Agent fails to address the task or provides completely irrelevant response'
      }
    ];
  }

  /**
   * Analyze completeness aspects of the response
   * @param {Object} data - Evaluation data
   * @returns {Object} Completeness analysis
   */
  analyzeCompleteness(data) {
    const analysis = {
      taskComplexity: 'unknown',
      responseLength: 0,
      addressedAspects: [],
      missingAspects: [],
      completenessIndicators: []
    };

    let task, response;
    
    if (data.prompt && data.response) {
      task = data.prompt;
      response = data.response;
    } else if (data.task && data.output) {
      task = data.task;
      response = data.output;
    } else if (data.instruction && data.result) {
      task = data.instruction;
      response = data.result;
    }

    if (task && response) {
      analysis.responseLength = response.length;
      
      // Analyze task complexity
      analysis.taskComplexity = this.assessTaskComplexity(task);
      
      // Look for completeness indicators
      analysis.completenessIndicators = this.findCompletenessIndicators(task, response);
      
      // Identify addressed aspects
      analysis.addressedAspects = this.identifyAddressedAspects(task, response);
    }

    return analysis;
  }

  /**
   * Assess the complexity of the given task
   * @param {string} task - Task description
   * @returns {string} Complexity level
   */
  assessTaskComplexity(task) {
    const taskLower = task.toLowerCase();
    
    // Count complexity indicators
    let complexityScore = 0;
    
    // Multiple requirements
    if (taskLower.includes(' and ') || taskLower.includes(', ')) {
      complexityScore += 1;
    }
    
    // Technical implementation
    if (taskLower.includes('implement') || taskLower.includes('create') || taskLower.includes('build')) {
      complexityScore += 1;
    }
    
    // Multiple steps
    if (taskLower.includes('first') || taskLower.includes('then') || taskLower.includes('step')) {
      complexityScore += 1;
    }
    
    // Error handling requirements
    if (taskLower.includes('error') || taskLower.includes('exception') || taskLower.includes('handle')) {
      complexityScore += 1;
    }
    
    // Testing requirements
    if (taskLower.includes('test') || taskLower.includes('validate')) {
      complexityScore += 1;
    }
    
    // Documentation requirements
    if (taskLower.includes('document') || taskLower.includes('comment') || taskLower.includes('explain')) {
      complexityScore += 1;
    }

    if (complexityScore >= 4) {
      return 'high';
    } else if (complexityScore >= 2) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * Find indicators of completeness in the response
   * @param {string} task - Task description
   * @param {string} response - Agent response
   * @returns {Array<string>} Completeness indicators
   */
  findCompletenessIndicators(task, response) {
    const indicators = [];
    const responseLower = response.toLowerCase();
    
    // Implementation indicators
    if (responseLower.includes('function') || responseLower.includes('class') || responseLower.includes('def ')) {
      indicators.push('Contains implementation code');
    }
    
    // Error handling indicators
    if (responseLower.includes('try') || responseLower.includes('catch') || responseLower.includes('error')) {
      indicators.push('Includes error handling');
    }
    
    // Testing indicators
    if (responseLower.includes('test') || responseLower.includes('assert') || responseLower.includes('expect')) {
      indicators.push('Includes testing code');
    }
    
    // Documentation indicators
    if (responseLower.includes('/**') || responseLower.includes('//') || responseLower.includes('#')) {
      indicators.push('Includes documentation/comments');
    }
    
    // Example indicators
    if (responseLower.includes('example') || responseLower.includes('usage')) {
      indicators.push('Provides examples or usage');
    }
    
    // Validation indicators
    if (responseLower.includes('validate') || responseLower.includes('check') || responseLower.includes('verify')) {
      indicators.push('Includes validation logic');
    }

    return indicators;
  }

  /**
   * Identify which aspects of the task were addressed
   * @param {string} task - Task description
   * @param {string} response - Agent response
   * @returns {Array<string>} Addressed aspects
   */
  identifyAddressedAspects(task, response) {
    const aspects = [];
    const taskLower = task.toLowerCase();
    const responseLower = response.toLowerCase();
    
    // Extract key verbs and nouns from task
    const taskWords = taskLower.split(/\s+/);
    const responseWords = responseLower.split(/\s+/);
    
    // Look for action words that appear in both task and response
    const actionWords = ['create', 'implement', 'build', 'write', 'add', 'remove', 'update', 'modify', 'fix', 'test', 'validate', 'handle', 'process', 'generate', 'parse', 'format'];
    
    for (const action of actionWords) {
      if (taskWords.includes(action) && responseWords.includes(action)) {
        aspects.push(`Addresses "${action}" requirement`);
      }
    }
    
    // Look for technical terms that appear in both
    const technicalTerms = ['function', 'class', 'method', 'variable', 'parameter', 'return', 'import', 'export', 'module', 'component', 'service', 'api', 'database', 'file', 'data'];
    
    for (const term of technicalTerms) {
      if (taskWords.includes(term) && responseWords.includes(term)) {
        aspects.push(`Addresses "${term}" aspect`);
      }
    }

    return aspects;
  }

  /**
   * Get completeness category based on score
   * @param {number} score - Completeness score (0-10)
   * @returns {string} Category description
   */
  getCompletenessCategory(score) {
    if (score >= 9) {
      return 'Comprehensive';
    } else if (score >= 7) {
      return 'Mostly Complete';
    } else if (score >= 5) {
      return 'Partially Complete';
    } else if (score >= 3) {
      return 'Incomplete';
    } else {
      return 'Severely Incomplete';
    }
  }

  /**
   * Generate detailed completeness report
   * @param {Object} data - Evaluation data
   * @param {number} score - Completeness score
   * @returns {Object} Detailed report
   */
  generateCompletenessReport(data, score) {
    const analysis = this.analyzeCompleteness(data);
    
    return {
      score,
      category: this.getCompletenessCategory(score),
      analysis,
      recommendations: this.generateRecommendations(score, analysis)
    };
  }

  /**
   * Generate recommendations for improving completeness
   * @param {number} score - Current completeness score
   * @param {Object} analysis - Completeness analysis
   * @returns {Array<string>} Recommendations
   */
  generateRecommendations(score, analysis) {
    const recommendations = [];
    
    if (score < 7) {
      recommendations.push('Review the original task to ensure all requirements are addressed');
      recommendations.push('Check for any missing components or features');
    }
    
    if (score < 5) {
      recommendations.push('Consider breaking down the task into smaller parts and addressing each systematically');
      recommendations.push('Ensure the response scope matches the task scope');
    }
    
    if (analysis.completenessIndicators.length < 3) {
      recommendations.push('Add more implementation details, error handling, or documentation as appropriate');
    }
    
    if (analysis.taskComplexity === 'high' && score < 8) {
      recommendations.push('For complex tasks, ensure all sub-requirements and edge cases are considered');
    }

    return recommendations;
  }
}

module.exports = { CompletenessMetric };
