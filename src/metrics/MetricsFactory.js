// Import metric classes (will be implemented in subsequent tasks)
const { ResponseTimeMetric } = require('./ResponseTimeMetric');
const { DiffMetric } = require('./DiffMetric');
const { ASTSimilarityMetric } = require('./ASTSimilarityMetric');
const { CompletenessMetric } = require('./CompletenessMetric');
const { TechnicalCorrectnessMetric } = require('./TechnicalCorrectnessMetric');
const { LogicalCorrectnessMetric } = require('./LogicalCorrectnessMetric');
const { ClarityMetric } = require('./ClarityMetric');
const { InstructionAdherenceMetric } = require('./InstructionAdherenceMetric');

/**
 * Metrics Factory - Creates and manages metric instances
 */
class MetricsFactory {
  constructor() {
    this.metrics = new Map();
    this.registerDefaultMetrics();
  }

  /**
   * Register default metrics
   */
  registerDefaultMetrics() {
    // Deterministic metrics
    this.registerMetric('response_time', ResponseTimeMetric);
    this.registerMetric('diff_metrics', DiffMetric);
    this.registerMetric('ast_similarity', ASTSimilarityMetric);

    // LLM-assessed metrics
    this.registerMetric('completeness', CompletenessMetric);
    this.registerMetric('technical_correctness', TechnicalCorrectnessMetric);
    this.registerMetric('functional_correctness', LogicalCorrectnessMetric);
    this.registerMetric('clarity', ClarityMetric);
    this.registerMetric('instruction_adherence', InstructionAdherenceMetric);
  }

  /**
   * Register a metric class
   * @param {string} name - Metric name
   * @param {Class} MetricClass - Metric class constructor
   */
  registerMetric(name, MetricClass) {
    this.metrics.set(name, MetricClass);
  }

  /**
   * Create a metric instance
   * @param {string} name - Metric name
   * @param {Object} config - Metric configuration
   * @returns {BaseMetric} Metric instance
   */
  createMetric(name, config = {}) {
    const MetricClass = this.metrics.get(name);
    
    if (!MetricClass) {
      throw new Error(`Unknown metric: ${name}. Available metrics: ${this.getAvailableMetrics().join(', ')}`);
    }

    try {
      return new MetricClass(config);
    } catch (error) {
      throw new Error(`Failed to create metric ${name}: ${error.message}`);
    }
  }

  /**
   * Create multiple metric instances
   * @param {Array<string>} names - Array of metric names
   * @param {Object} globalConfig - Global configuration for all metrics
   * @param {Object} specificConfigs - Metric-specific configurations
   * @returns {Array<BaseMetric>} Array of metric instances
   */
  createMetrics(names, globalConfig = {}, specificConfigs = {}) {
    const metrics = [];
    
    for (const name of names) {
      const metricConfig = {
        ...globalConfig,
        ...specificConfigs[name]
      };
      
      try {
        const metric = this.createMetric(name, metricConfig);
        metrics.push(metric);
      } catch (error) {
        throw new Error(`Failed to create metric ${name}: ${error.message}`);
      }
    }
    
    return metrics;
  }

  /**
   * Get list of available metric names
   * @returns {Array<string>} Available metric names
   */
  getAvailableMetrics() {
    return Array.from(this.metrics.keys());
  }

  /**
   * Check if a metric is registered
   * @param {string} name - Metric name
   * @returns {boolean} True if metric is registered
   */
  hasMetric(name) {
    return this.metrics.has(name);
  }

  /**
   * Validate that all requested metrics are available
   * @param {Array<string>} names - Array of metric names to validate
   * @returns {Object} Validation result
   */
  validateMetrics(names) {
    const results = {
      valid: true,
      missing: [],
      available: this.getAvailableMetrics()
    };

    for (const name of names) {
      if (!this.hasMetric(name)) {
        results.valid = false;
        results.missing.push(name);
      }
    }

    return results;
  }

  /**
   * Get metric information
   * @param {string} name - Metric name
   * @returns {Object} Metric information
   */
  getMetricInfo(name) {
    const MetricClass = this.metrics.get(name);
    
    if (!MetricClass) {
      return null;
    }

    // Create a temporary instance to get information
    try {
      const tempMetric = new MetricClass();
      return tempMetric.getMetadata();
    } catch (error) {
      return {
        name: name,
        className: MetricClass.name,
        error: error.message
      };
    }
  }

  /**
   * Get information for all registered metrics
   * @returns {Array<Object>} Array of metric information
   */
  getAllMetricInfo() {
    return this.getAvailableMetrics().map(name => this.getMetricInfo(name));
  }

  /**
   * Filter metrics by type
   * @param {string} type - Metric type ('deterministic' or 'llm_assessed')
   * @returns {Array<string>} Array of metric names of the specified type
   */
  getMetricsByType(type) {
    const metricNames = [];
    
    for (const name of this.getAvailableMetrics()) {
      const info = this.getMetricInfo(name);
      if (info && info.type === type) {
        metricNames.push(name);
      }
    }
    
    return metricNames;
  }

  /**
   * Filter metrics by applicable mode
   * @param {string} mode - Mode name ('LLM_Evaluator' or 'PR_Recreate')
   * @returns {Array<string>} Array of metric names applicable to the mode
   */
  getMetricsForMode(mode) {
    const metricNames = [];
    
    for (const name of this.getAvailableMetrics()) {
      const info = this.getMetricInfo(name);
      if (info && info.applicableModes && info.applicableModes.includes(mode)) {
        metricNames.push(name);
      }
    }
    
    return metricNames;
  }

  /**
   * Validate metrics for a specific mode
   * @param {Array<string>} metricNames - Metric names to validate
   * @param {string} mode - Mode name
   * @returns {Object} Validation result
   */
  validateMetricsForMode(metricNames, mode) {
    const results = {
      valid: true,
      invalidMetrics: [],
      validMetrics: []
    };

    for (const name of metricNames) {
      const info = this.getMetricInfo(name);
      
      if (!info) {
        results.valid = false;
        results.invalidMetrics.push({
          name,
          reason: 'Metric not found'
        });
        continue;
      }

      if (!info.applicableModes || !info.applicableModes.includes(mode)) {
        results.valid = false;
        results.invalidMetrics.push({
          name,
          reason: `Not applicable for mode: ${mode}`,
          applicableModes: info.applicableModes
        });
      } else {
        results.validMetrics.push(name);
      }
    }

    return results;
  }

  /**
   * Create metrics from settings configuration
   * @param {Object} settings - Settings object from SettingsManager
   * @returns {Array<BaseMetric>} Array of configured metric instances
   */
  createMetricsFromSettings(settings) {
    const { metrics: metricNames, mode } = settings;
    
    // Validate that all requested metrics are available
    const validation = this.validateMetrics(metricNames);
    if (!validation.valid) {
      throw new Error(`Unknown metrics: ${validation.missing.join(', ')}. Available: ${validation.available.join(', ')}`);
    }

    // Validate metrics for the specified mode
    const modeValidation = this.validateMetricsForMode(metricNames, mode);
    if (!modeValidation.valid) {
      const errors = modeValidation.invalidMetrics.map(m => 
        `${m.name}: ${m.reason}`
      ).join('\n');
      throw new Error(`Metric validation failed for mode ${mode}:\n${errors}`);
    }

    // Create metric instances
    const globalConfig = {
      mode: mode
    };

    // Extract any metric-specific configurations from settings
    const specificConfigs = {};
    
    return this.createMetrics(metricNames, globalConfig, specificConfigs);
  }

  /**
   * Calculate all metrics for given data
   * @param {Array<BaseMetric>} metrics - Metric instances
   * @param {Object} data - Data to calculate metrics from
   * @param {Object} context - Calculation context
   * @returns {Promise<Object>} Object with metric results
   */
  async calculateAllMetrics(metrics, data, context) {
    const results = {};
    const errors = {};

    // Calculate metrics in parallel for better performance
    const calculations = metrics.map(async (metric) => {
      try {
        const value = await metric.calculate(data, context);
        results[metric.name] = value;
      } catch (error) {
        errors[metric.name] = error.message;
        results[metric.name] = 'NA';
      }
    });

    await Promise.all(calculations);

    return {
      results,
      errors: Object.keys(errors).length > 0 ? errors : null
    };
  }

  /**
   * Get metric calculation summary
   * @param {Array<BaseMetric>} metrics - Metric instances
   * @returns {Object} Summary information
   */
  getCalculationSummary(metrics) {
    const summary = {
      totalMetrics: metrics.length,
      deterministic: 0,
      llmAssessed: 0,
      byType: {},
      estimatedTime: 0
    };

    for (const metric of metrics) {
      const info = metric.getMetadata();
      
      if (info.type === 'deterministic') {
        summary.deterministic++;
        summary.estimatedTime += 100; // ~100ms for deterministic metrics
      } else if (info.type === 'llm_assessed') {
        summary.llmAssessed++;
        summary.estimatedTime += 5000; // ~5s for LLM metrics
      }

      if (!summary.byType[info.type]) {
        summary.byType[info.type] = [];
      }
      summary.byType[info.type].push(metric.name);
    }

    return summary;
  }
}

// Export singleton instance
module.exports = { MetricsFactory: new MetricsFactory() };
