const fs = require('fs');
const path = require('path');
const { ChartJSNodeCanvas } = require('chartjs-node-canvas');
const { logger } = require('./Logger');
const { fileSystem } = require('./FileSystem');

/**
 * Chart Generator - Creates visualization charts from benchmark results
 * Supports PNG output with agent-specific colors and multiple chart types
 */
class ChartGenerator {
  constructor(options = {}) {
    this.options = {
      width: 800,
      height: 600,
      backgroundColor: 'white',
      outputDir: './charts',
      ...options
    };

    this.logger = logger.child({ component: 'ChartGenerator' });
    
    // Agent-specific colors
    this.agentColors = {
      'Augment CLI': '#22c55e',      // Green
      'Claude Code': '#f97316',      // Orange  
      'Cursor CLI': '#3b82f6',       // Blue
      'default': '#6b7280'           // Gray
    };

    // Initialize Chart.js canvas
    this.chartJSNodeCanvas = new ChartJSNodeCanvas({
      width: this.options.width,
      height: this.options.height,
      backgroundColor: this.options.backgroundColor
    });

    this.initializeOutputDirectory();
  }

  /**
   * Initialize output directory
   */
  async initializeOutputDirectory() {
    try {
      await fileSystem.ensureDir(this.options.outputDir);
      this.logger.debug('Chart output directory initialized', { 
        dir: this.options.outputDir 
      });
    } catch (error) {
      this.logger.error('Failed to initialize chart output directory', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Generate all charts from results data
   * @param {Object} resultsData - Benchmark results data
   * @param {string} outputPrefix - Prefix for output files
   * @returns {Promise<Array<string>>} Generated chart file paths
   */
  async generateAllCharts(resultsData, outputPrefix = 'benchmark') {
    try {
      this.logger.info('Generating all charts', { 
        outputPrefix,
        agents: Object.keys(this.extractAgents(resultsData)).length
      });

      const chartPaths = [];

      // Generate metric comparison charts
      const metricCharts = await this.generateMetricCharts(resultsData, outputPrefix);
      chartPaths.push(...metricCharts);

      // Generate agent performance overview
      const overviewChart = await this.generateAgentOverview(resultsData, outputPrefix);
      chartPaths.push(overviewChart);

      // Generate execution time chart
      const timeChart = await this.generateExecutionTimeChart(resultsData, outputPrefix);
      chartPaths.push(timeChart);

      // Generate success rate chart
      const successChart = await this.generateSuccessRateChart(resultsData, outputPrefix);
      chartPaths.push(successChart);

      this.logger.info('All charts generated successfully', { 
        count: chartPaths.length,
        charts: chartPaths
      });

      return chartPaths;
    } catch (error) {
      this.logger.error('Failed to generate charts', { error: error.message });
      throw error;
    }
  }

  /**
   * Generate charts for each metric
   * @param {Object} resultsData - Results data
   * @param {string} outputPrefix - Output prefix
   * @returns {Promise<Array<string>>} Chart file paths
   */
  async generateMetricCharts(resultsData, outputPrefix) {
    try {
      const chartPaths = [];
      const metrics = this.extractMetrics(resultsData);

      for (const metricName of metrics) {
        const chartPath = await this.generateMetricChart(
          resultsData, 
          metricName, 
          `${outputPrefix}_metric_${metricName.toLowerCase().replace(/\s+/g, '_')}`
        );
        chartPaths.push(chartPath);
      }

      return chartPaths;
    } catch (error) {
      this.logger.error('Failed to generate metric charts', { error: error.message });
      throw error;
    }
  }

  /**
   * Generate chart for a specific metric
   * @param {Object} resultsData - Results data
   * @param {string} metricName - Metric name
   * @param {string} filename - Output filename
   * @returns {Promise<string>} Chart file path
   */
  async generateMetricChart(resultsData, metricName, filename) {
    try {
      this.logger.debug('Generating metric chart', { metricName, filename });

      const chartData = this.prepareMetricChartData(resultsData, metricName);
      
      const config = {
        type: 'bar',
        data: chartData,
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: `${metricName} Comparison by Agent`,
              font: { size: 16 }
            },
            legend: {
              display: true,
              position: 'top'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: this.getMetricUnit(metricName)
              }
            },
            x: {
              title: {
                display: true,
                text: 'Agents'
              }
            }
          }
        }
      };

      const chartPath = await this.renderChart(config, filename);
      return chartPath;
    } catch (error) {
      this.logger.error('Failed to generate metric chart', { 
        metricName, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Generate agent performance overview chart
   * @param {Object} resultsData - Results data
   * @param {string} outputPrefix - Output prefix
   * @returns {Promise<string>} Chart file path
   */
  async generateAgentOverview(resultsData, outputPrefix) {
    try {
      this.logger.debug('Generating agent overview chart');

      const chartData = this.prepareAgentOverviewData(resultsData);
      
      const config = {
        type: 'radar',
        data: chartData,
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: 'Agent Performance Overview',
              font: { size: 16 }
            },
            legend: {
              display: true,
              position: 'top'
            }
          },
          scales: {
            r: {
              beginAtZero: true,
              max: 10,
              ticks: {
                stepSize: 2
              }
            }
          }
        }
      };

      const filename = `${outputPrefix}_agent_overview`;
      const chartPath = await this.renderChart(config, filename);
      return chartPath;
    } catch (error) {
      this.logger.error('Failed to generate agent overview chart', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Generate execution time chart
   * @param {Object} resultsData - Results data
   * @param {string} outputPrefix - Output prefix
   * @returns {Promise<string>} Chart file path
   */
  async generateExecutionTimeChart(resultsData, outputPrefix) {
    try {
      this.logger.debug('Generating execution time chart');

      const chartData = this.prepareExecutionTimeData(resultsData);
      
      const config = {
        type: 'line',
        data: chartData,
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: 'Execution Time by Agent',
              font: { size: 16 }
            },
            legend: {
              display: true,
              position: 'top'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Time (ms)'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Prompts/Tasks'
              }
            }
          }
        }
      };

      const filename = `${outputPrefix}_execution_time`;
      const chartPath = await this.renderChart(config, filename);
      return chartPath;
    } catch (error) {
      this.logger.error('Failed to generate execution time chart', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Generate success rate chart
   * @param {Object} resultsData - Results data
   * @param {string} outputPrefix - Output prefix
   * @returns {Promise<string>} Chart file path
   */
  async generateSuccessRateChart(resultsData, outputPrefix) {
    try {
      this.logger.debug('Generating success rate chart');

      const chartData = this.prepareSuccessRateData(resultsData);
      
      const config = {
        type: 'doughnut',
        data: chartData,
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: 'Success Rate by Agent',
              font: { size: 16 }
            },
            legend: {
              display: true,
              position: 'right'
            }
          }
        }
      };

      const filename = `${outputPrefix}_success_rate`;
      const chartPath = await this.renderChart(config, filename);
      return chartPath;
    } catch (error) {
      this.logger.error('Failed to generate success rate chart', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Prepare chart data for a specific metric
   * @param {Object} resultsData - Results data
   * @param {string} metricName - Metric name
   * @returns {Object} Chart data
   */
  prepareMetricChartData(resultsData, metricName) {
    const agents = this.extractAgents(resultsData);
    const labels = Object.keys(agents);
    
    const data = labels.map(agent => {
      const agentResults = agents[agent];
      const metricValues = agentResults
        .map(result => result.metrics && result.metrics[metricName])
        .filter(value => value !== undefined && value !== null);
      
      if (metricValues.length === 0) return 0;
      
      // Calculate average
      return metricValues.reduce((sum, val) => sum + val, 0) / metricValues.length;
    });

    const colors = labels.map(agent => this.getAgentColor(agent));

    return {
      labels: labels,
      datasets: [{
        label: metricName,
        data: data,
        backgroundColor: colors,
        borderColor: colors,
        borderWidth: 1
      }]
    };
  }

  /**
   * Prepare agent overview radar chart data
   * @param {Object} resultsData - Results data
   * @returns {Object} Chart data
   */
  prepareAgentOverviewData(resultsData) {
    const agents = this.extractAgents(resultsData);
    const metrics = this.extractMetrics(resultsData);
    
    const datasets = Object.keys(agents).map(agentName => {
      const agentResults = agents[agentName];
      const agentColor = this.getAgentColor(agentName);
      
      const data = metrics.map(metricName => {
        const metricValues = agentResults
          .map(result => result.metrics && result.metrics[metricName])
          .filter(value => value !== undefined && value !== null);
        
        if (metricValues.length === 0) return 0;
        
        // Calculate average and normalize to 0-10 scale
        const average = metricValues.reduce((sum, val) => sum + val, 0) / metricValues.length;
        return this.normalizeMetricValue(metricName, average);
      });

      return {
        label: agentName,
        data: data,
        borderColor: agentColor,
        backgroundColor: agentColor + '20', // Add transparency
        pointBackgroundColor: agentColor,
        pointBorderColor: agentColor,
        pointHoverBackgroundColor: agentColor,
        pointHoverBorderColor: agentColor
      };
    });

    return {
      labels: metrics,
      datasets: datasets
    };
  }

  /**
   * Prepare execution time chart data
   * @param {Object} resultsData - Results data
   * @returns {Object} Chart data
   */
  prepareExecutionTimeData(resultsData) {
    const agents = this.extractAgents(resultsData);
    const prompts = this.extractPrompts(resultsData);
    
    const datasets = Object.keys(agents).map(agentName => {
      const agentResults = agents[agentName];
      const agentColor = this.getAgentColor(agentName);
      
      const data = prompts.map(promptId => {
        const promptResults = agentResults.filter(result => result.prompt === promptId);
        if (promptResults.length === 0) return null;
        
        const executionTimes = promptResults
          .map(result => result.executionTime)
          .filter(time => time !== undefined && time !== null);
        
        if (executionTimes.length === 0) return null;
        
        // Calculate average execution time
        return executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length;
      });

      return {
        label: agentName,
        data: data,
        borderColor: agentColor,
        backgroundColor: agentColor + '20',
        fill: false,
        tension: 0.1
      };
    });

    return {
      labels: prompts,
      datasets: datasets
    };
  }

  /**
   * Prepare success rate chart data
   * @param {Object} resultsData - Results data
   * @returns {Object} Chart data
   */
  prepareSuccessRateData(resultsData) {
    const agents = this.extractAgents(resultsData);
    const labels = [];
    const data = [];
    const colors = [];

    Object.keys(agents).forEach(agentName => {
      const agentResults = agents[agentName];
      const totalRuns = agentResults.length;
      const successfulRuns = agentResults.filter(result => result.success !== false).length;
      const successRate = totalRuns > 0 ? (successfulRuns / totalRuns) * 100 : 0;

      labels.push(`${agentName} (${successRate.toFixed(1)}%)`);
      data.push(successRate);
      colors.push(this.getAgentColor(agentName));
    });

    return {
      labels: labels,
      datasets: [{
        data: data,
        backgroundColor: colors,
        borderColor: colors,
        borderWidth: 1
      }]
    };
  }

  /**
   * Extract agents from results data
   * @param {Object} resultsData - Results data
   * @returns {Object} Agents grouped by name
   */
  extractAgents(resultsData) {
    const agents = {};
    
    if (resultsData.results) {
      for (const result of resultsData.results) {
        if (!agents[result.agent]) {
          agents[result.agent] = [];
        }
        agents[result.agent].push(...result.runs);
      }
    }

    return agents;
  }

  /**
   * Extract metrics from results data
   * @param {Object} resultsData - Results data
   * @returns {Array<string>} Metric names
   */
  extractMetrics(resultsData) {
    const metrics = new Set();
    
    if (resultsData.results) {
      for (const result of resultsData.results) {
        for (const run of result.runs) {
          if (run.metrics) {
            Object.keys(run.metrics).forEach(metric => metrics.add(metric));
          }
        }
      }
    }

    return Array.from(metrics);
  }

  /**
   * Extract prompts from results data
   * @param {Object} resultsData - Results data
   * @returns {Array<string>} Prompt IDs
   */
  extractPrompts(resultsData) {
    const prompts = new Set();
    
    if (resultsData.results) {
      for (const result of resultsData.results) {
        prompts.add(result.prompt);
      }
    }

    return Array.from(prompts);
  }

  /**
   * Get agent-specific color
   * @param {string} agentName - Agent name
   * @returns {string} Color hex code
   */
  getAgentColor(agentName) {
    return this.agentColors[agentName] || this.agentColors.default;
  }

  /**
   * Get metric unit for display
   * @param {string} metricName - Metric name
   * @returns {string} Unit string
   */
  getMetricUnit(metricName) {
    const unitMap = {
      'Response Time': 'ms',
      'Execution Time': 'ms',
      'Diff Metric': 'changes',
      'AST Similarity': 'similarity score',
      'Completeness': 'score (0-10)',
      'Technical Correctness': 'score (0-10)',
      'Logical Correctness': 'score (0-10)',
      'Clarity': 'score (0-10)',
      'Instruction Adherence': 'score (0-10)'
    };

    return unitMap[metricName] || 'value';
  }

  /**
   * Normalize metric value to 0-10 scale for radar chart
   * @param {string} metricName - Metric name
   * @param {number} value - Original value
   * @returns {number} Normalized value
   */
  normalizeMetricValue(metricName, value) {
    // Most metrics are already 0-10, but some need normalization
    if (metricName === 'Response Time' || metricName === 'Execution Time') {
      // Normalize time metrics (lower is better, so invert)
      const maxTime = 60000; // 1 minute max
      return Math.max(0, 10 - (value / maxTime) * 10);
    }

    if (metricName === 'Diff Metric') {
      // Normalize diff metric (assume max 100 changes)
      return Math.min(10, (value / 100) * 10);
    }

    // For 0-10 metrics, return as-is
    return Math.min(10, Math.max(0, value));
  }

  /**
   * Render chart to PNG file
   * @param {Object} config - Chart.js configuration
   * @param {string} filename - Output filename (without extension)
   * @returns {Promise<string>} File path
   */
  async renderChart(config, filename) {
    try {
      const buffer = await this.chartJSNodeCanvas.renderToBuffer(config);
      const filePath = path.join(this.options.outputDir, `${filename}.png`);
      
      await fs.promises.writeFile(filePath, buffer);
      
      this.logger.debug('Chart rendered successfully', { 
        filename: `${filename}.png`,
        path: filePath
      });

      return filePath;
    } catch (error) {
      this.logger.error('Failed to render chart', { 
        filename,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Generate chart summary report
   * @param {Array<string>} chartPaths - Generated chart file paths
   * @param {Object} resultsData - Results data
   * @returns {Promise<string>} Summary report path
   */
  async generateChartSummary(chartPaths, resultsData) {
    try {
      this.logger.debug('Generating chart summary report');

      const summary = {
        generated: new Date().toISOString(),
        charts: chartPaths.map(chartPath => ({
          name: path.basename(chartPath, '.png'),
          path: chartPath,
          type: this.getChartType(chartPath)
        })),
        statistics: {
          totalAgents: Object.keys(this.extractAgents(resultsData)).length,
          totalMetrics: this.extractMetrics(resultsData).length,
          totalPrompts: this.extractPrompts(resultsData).length
        }
      };

      const summaryPath = path.join(this.options.outputDir, 'chart_summary.json');
      await fileSystem.writeJson(summaryPath, summary);

      this.logger.info('Chart summary report generated', { 
        path: summaryPath,
        charts: chartPaths.length
      });

      return summaryPath;
    } catch (error) {
      this.logger.error('Failed to generate chart summary', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Get chart type from file path
   * @param {string} chartPath - Chart file path
   * @returns {string} Chart type
   */
  getChartType(chartPath) {
    const filename = path.basename(chartPath, '.png');
    
    if (filename.includes('metric_')) return 'metric';
    if (filename.includes('overview')) return 'overview';
    if (filename.includes('execution_time')) return 'execution_time';
    if (filename.includes('success_rate')) return 'success_rate';
    
    return 'unknown';
  }
}

// Create default instance
const chartGenerator = new ChartGenerator();

module.exports = { ChartGenerator, chartGenerator };
