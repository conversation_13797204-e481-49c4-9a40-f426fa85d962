const simpleGit = require('simple-git');
const path = require('path');
const { logger } = require('./Logger');
const { fileSystem } = require('./FileSystem');

/**
 * Git Manager - Handles git operations using simple-git
 */
class GitManager {
  constructor(options = {}) {
    this.options = {
      logger: logger,
      ...options
    };
    this.git = null;
    this.workingDirectory = null;
  }

  /**
   * Initialize git instance for a specific directory
   * @param {string} workingDir - Working directory path
   */
  init(workingDir) {
    this.workingDirectory = workingDir;
    this.git = simpleGit(workingDir);
    this.options.logger.debug('Git manager initialized', { workingDir });
  }

  /**
   * Clone repository
   * @param {string} repoUrl - Repository URL
   * @param {string} targetDir - Target directory
   * @param {Object} options - Clone options
   * @returns {Promise<void>}
   */
  async clone(repoUrl, targetDir, options = {}) {
    try {
      this.options.logger.info('Cloning repository', { repoUrl, targetDir });
      
      await fileSystem.ensureDir(path.dirname(targetDir));
      
      const cloneOptions = {
        '--depth': options.depth || null,
        '--branch': options.branch || null,
        '--single-branch': options.singleBranch || false,
        ...options.gitOptions
      };

      // Filter out null values
      const filteredOptions = Object.fromEntries(
        Object.entries(cloneOptions).filter(([_, value]) => value !== null)
      );

      await simpleGit().clone(repoUrl, targetDir, filteredOptions);
      
      this.options.logger.info('Repository cloned successfully', { 
        repoUrl, 
        targetDir 
      });
      
      // Initialize git instance for the cloned repo
      this.init(targetDir);
      
    } catch (error) {
      this.options.logger.error('Failed to clone repository', { 
        repoUrl, 
        targetDir, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Get current branch
   * @returns {Promise<string>} Current branch name
   */
  async getCurrentBranch() {
    try {
      const status = await this.git.status();
      this.options.logger.debug('Current branch retrieved', { 
        branch: status.current 
      });
      return status.current;
    } catch (error) {
      this.options.logger.error('Failed to get current branch', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Checkout branch
   * @param {string} branch - Branch name
   * @param {boolean} create - Create branch if it doesn't exist
   * @returns {Promise<void>}
   */
  async checkout(branch, create = false) {
    try {
      this.options.logger.info('Checking out branch', { branch, create });
      
      if (create) {
        await this.git.checkoutLocalBranch(branch);
      } else {
        await this.git.checkout(branch);
      }
      
      this.options.logger.info('Branch checked out successfully', { branch });
    } catch (error) {
      this.options.logger.error('Failed to checkout branch', { 
        branch, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Create and checkout new branch
   * @param {string} branch - Branch name
   * @returns {Promise<void>}
   */
  async createBranch(branch) {
    return this.checkout(branch, true);
  }

  /**
   * Get commit history
   * @param {Object} options - Log options
   * @returns {Promise<Array>} Array of commit objects
   */
  async getCommitHistory(options = {}) {
    try {
      const logOptions = {
        maxCount: options.maxCount || 100,
        from: options.from || null,
        to: options.to || null,
        ...options
      };

      const log = await this.git.log(logOptions);
      
      this.options.logger.debug('Commit history retrieved', { 
        count: log.all.length 
      });
      
      return log.all;
    } catch (error) {
      this.options.logger.error('Failed to get commit history', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Get merged pull requests from commit history
   * @param {number} maxPRs - Maximum number of PRs to find
   * @param {number} maxAge - Maximum age in days (default: 730 = 2 years)
   * @returns {Promise<Array>} Array of PR commit objects
   */
  async getMergedPullRequests(maxPRs = 10, maxAge = 730) {
    try {
      this.options.logger.info('Finding merged pull requests', { maxPRs, maxAge });
      
      const since = new Date();
      since.setDate(since.getDate() - maxAge);
      
      const commits = await this.getCommitHistory({
        maxCount: 1000,
        since: since.toISOString()
      });

      // Filter for merge commits that look like PR merges
      const prCommits = commits.filter(commit => {
        const message = commit.message.toLowerCase();
        return (
          commit.refs && 
          (message.includes('merge pull request') || 
           message.includes('merge branch') ||
           message.match(/^merge #\d+/))
        );
      }).slice(0, maxPRs);

      this.options.logger.info('Found merged pull requests', { 
        count: prCommits.length 
      });
      
      return prCommits;
    } catch (error) {
      this.options.logger.error('Failed to get merged pull requests', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Cherry-pick commit
   * @param {string} commitHash - Commit hash to cherry-pick
   * @returns {Promise<void>}
   */
  async cherryPick(commitHash) {
    try {
      this.options.logger.info('Cherry-picking commit', { commitHash });
      
      await this.git.raw(['cherry-pick', commitHash]);
      
      this.options.logger.info('Cherry-pick completed', { commitHash });
    } catch (error) {
      this.options.logger.error('Failed to cherry-pick commit', { 
        commitHash, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Add files to staging
   * @param {string|Array} files - Files to add (. for all)
   * @returns {Promise<void>}
   */
  async add(files = '.') {
    try {
      await this.git.add(files);
      this.options.logger.debug('Files added to staging', { files });
    } catch (error) {
      this.options.logger.error('Failed to add files', { 
        files, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Commit changes
   * @param {string} message - Commit message
   * @returns {Promise<Object>} Commit result
   */
  async commit(message) {
    try {
      this.options.logger.info('Creating commit', { message });
      
      const result = await this.git.commit(message);
      
      this.options.logger.info('Commit created', { 
        hash: result.commit,
        message 
      });
      
      return result;
    } catch (error) {
      this.options.logger.error('Failed to create commit', { 
        message, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Get diff between commits/branches
   * @param {string} from - From commit/branch
   * @param {string} to - To commit/branch
   * @param {Object} options - Diff options
   * @returns {Promise<string>} Diff output
   */
  async getDiff(from, to, options = {}) {
    try {
      const diffOptions = [];
      
      if (options.nameOnly) {
        diffOptions.push('--name-only');
      }
      
      if (options.stat) {
        diffOptions.push('--stat');
      }

      const diff = await this.git.diff([from, to, ...diffOptions]);
      
      this.options.logger.debug('Diff generated', { 
        from, 
        to, 
        size: diff.length 
      });
      
      return diff;
    } catch (error) {
      this.options.logger.error('Failed to generate diff', { 
        from, 
        to, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Get file changes between commits
   * @param {string} from - From commit/branch
   * @param {string} to - To commit/branch
   * @returns {Promise<Object>} File change statistics
   */
  async getFileChanges(from, to) {
    try {
      const nameOnlyDiff = await this.getDiff(from, to, { nameOnly: true });
      const statDiff = await this.getDiff(from, to, { stat: true });
      
      const changedFiles = nameOnlyDiff
        .split('\n')
        .filter(line => line.trim())
        .length;

      // Parse stat output for line changes
      const statLines = statDiff.split('\n');
      const summaryLine = statLines[statLines.length - 2] || '';
      const matches = summaryLine.match(/(\d+) files? changed(?:, (\d+) insertions?\(\+\))?(?:, (\d+) deletions?\(-\))?/);
      
      const result = {
        files_changed: changedFiles,
        files_added: 0, // Would need more detailed analysis
        files_modified: changedFiles,
        files_deleted: 0, // Would need more detailed analysis
        lines_added: matches ? parseInt(matches[2] || '0') : 0,
        lines_deleted: matches ? parseInt(matches[3] || '0') : 0
      };

      this.options.logger.debug('File changes calculated', result);
      
      return result;
    } catch (error) {
      this.options.logger.error('Failed to get file changes', { 
        from, 
        to, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Create git worktree
   * @param {string} path - Worktree path
   * @param {string} branch - Branch for worktree
   * @returns {Promise<void>}
   */
  async createWorktree(path, branch) {
    try {
      this.options.logger.info('Creating git worktree', { path, branch });
      
      await this.git.raw(['worktree', 'add', path, branch]);
      
      this.options.logger.info('Worktree created', { path, branch });
    } catch (error) {
      this.options.logger.error('Failed to create worktree', { 
        path, 
        branch, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Remove git worktree
   * @param {string} path - Worktree path
   * @returns {Promise<void>}
   */
  async removeWorktree(path) {
    try {
      this.options.logger.info('Removing git worktree', { path });
      
      await this.git.raw(['worktree', 'remove', path]);
      
      this.options.logger.info('Worktree removed', { path });
    } catch (error) {
      this.options.logger.error('Failed to remove worktree', { 
        path, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Get repository status
   * @returns {Promise<Object>} Repository status
   */
  async getStatus() {
    try {
      const status = await this.git.status();
      this.options.logger.debug('Repository status retrieved', { 
        current: status.current,
        modified: status.modified.length,
        created: status.created.length,
        deleted: status.deleted.length
      });
      return status;
    } catch (error) {
      this.options.logger.error('Failed to get repository status', { 
        error: error.message 
      });
      throw error;
    }
  }
}

module.exports = { GitManager };
