const axios = require('axios');
const { logger } = require('./Logger');
const { fileSystem } = require('./FileSystem');
const { gitManager } = require('./GitManager');

/**
 * Prompt Generator - Generates prompts from repository summaries and topics
 * Supports OpenAI and Anthropic APIs with configurable endpoints
 */
class PromptGenerator {
  constructor(config = {}) {
    this.config = {
      llmProvider: process.env.LLM_PROVIDER || 'openai',
      llmModel: process.env.LLM_MODEL || 'gpt-4',
      llmApiKey: process.env.LLM_API_KEY || '',
      llmLocalEndpoint: process.env.LLM_LOCAL_ENDPOINT || '',
      maxTokens: 4000,
      temperature: 0.7,
      timeout: 30000,
      maxRetries: 3,
      retryDelay: 1000,
      ...config
    };

    this.logger = logger.child({ component: 'PromptGenerator' });
    this.validateConfiguration();
  }

  /**
   * Validate configuration
   */
  validateConfiguration() {
    if (!this.config.llmProvider) {
      throw new Error('LLM provider must be specified');
    }

    if (!['openai', 'anthropic', 'local'].includes(this.config.llmProvider)) {
      throw new Error('LLM provider must be one of: openai, anthropic, local');
    }

    if (this.config.llmProvider !== 'local' && !this.config.llmApiKey) {
      this.logger.warn('No LLM API key provided - some features may not work');
    }
  }

  /**
   * Generate prompts from repository analysis
   * @param {string} repoPath - Path to repository
   * @param {Object} options - Generation options
   * @returns {Promise<Array<Object>>} Generated prompts
   */
  async generatePromptsFromRepository(repoPath, options = {}) {
    try {
      const {
        numPrompts = 5,
        topics = [],
        complexity = 'medium',
        includeContext = true,
        promptTypes = ['implementation', 'debugging', 'enhancement', 'refactoring']
      } = options;

      this.logger.info('Generating prompts from repository', { 
        repoPath, 
        numPrompts, 
        topics: topics.length,
        complexity 
      });

      // Analyze repository
      const repoAnalysis = await this.analyzeRepository(repoPath);
      
      // Generate repository summary
      const repoSummary = await this.generateRepositorySummary(repoAnalysis);
      
      // Generate prompts based on analysis
      const prompts = await this.generatePrompts({
        repoSummary,
        repoAnalysis,
        numPrompts,
        topics,
        complexity,
        promptTypes,
        includeContext
      });

      this.logger.info('Prompts generated successfully', { 
        count: prompts.length 
      });

      return prompts;
    } catch (error) {
      this.logger.error('Failed to generate prompts from repository', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Analyze repository structure and content
   * @param {string} repoPath - Path to repository
   * @returns {Promise<Object>} Repository analysis
   */
  async analyzeRepository(repoPath) {
    const analysis = {
      structure: {},
      languages: {},
      files: [],
      dependencies: {},
      readme: '',
      gitInfo: {},
      codePatterns: [],
      complexity: 'unknown'
    };

    try {
      // Analyze directory structure
      analysis.structure = await this.analyzeDirectoryStructure(repoPath);
      
      // Detect languages and file types
      analysis.languages = await this.detectLanguages(repoPath);
      
      // Get file list
      analysis.files = await this.getFileList(repoPath);
      
      // Read README if exists
      analysis.readme = await this.readReadme(repoPath);
      
      // Get git information
      analysis.gitInfo = await this.getGitInfo(repoPath);
      
      // Analyze dependencies
      analysis.dependencies = await this.analyzeDependencies(repoPath);
      
      // Detect code patterns
      analysis.codePatterns = await this.detectCodePatterns(repoPath);
      
      // Assess complexity
      analysis.complexity = this.assessRepositoryComplexity(analysis);

      return analysis;
    } catch (error) {
      this.logger.error('Repository analysis failed', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Analyze directory structure
   * @param {string} repoPath - Repository path
   * @returns {Promise<Object>} Directory structure
   */
  async analyzeDirectoryStructure(repoPath) {
    const structure = {
      directories: [],
      depth: 0,
      patterns: []
    };

    try {
      const files = await fileSystem.readdir(repoPath);
      
      for (const file of files) {
        const filePath = `${repoPath}/${file}`;
        const stats = await fileSystem.stat(filePath);
        
        if (stats.isDirectory() && !file.startsWith('.')) {
          structure.directories.push(file);
        }
      }

      // Detect common patterns
      if (structure.directories.includes('src')) {
        structure.patterns.push('src-based');
      }
      if (structure.directories.includes('lib')) {
        structure.patterns.push('lib-based');
      }
      if (structure.directories.includes('test') || structure.directories.includes('tests')) {
        structure.patterns.push('has-tests');
      }
      if (structure.directories.includes('docs')) {
        structure.patterns.push('documented');
      }

      return structure;
    } catch (error) {
      this.logger.warn('Failed to analyze directory structure', { 
        error: error.message 
      });
      return structure;
    }
  }

  /**
   * Detect programming languages in repository
   * @param {string} repoPath - Repository path
   * @returns {Promise<Object>} Language statistics
   */
  async detectLanguages(repoPath) {
    const languages = {};
    
    try {
      const files = await this.getFileList(repoPath);
      
      for (const file of files) {
        const extension = file.split('.').pop().toLowerCase();
        const language = this.getLanguageFromExtension(extension);
        
        if (language) {
          languages[language] = (languages[language] || 0) + 1;
        }
      }

      return languages;
    } catch (error) {
      this.logger.warn('Failed to detect languages', { 
        error: error.message 
      });
      return languages;
    }
  }

  /**
   * Get language from file extension
   * @param {string} extension - File extension
   * @returns {string|null} Language name
   */
  getLanguageFromExtension(extension) {
    const extensionMap = {
      'js': 'JavaScript',
      'ts': 'TypeScript',
      'py': 'Python',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C',
      'go': 'Go',
      'rs': 'Rust',
      'rb': 'Ruby',
      'php': 'PHP',
      'cs': 'C#',
      'swift': 'Swift',
      'kt': 'Kotlin',
      'scala': 'Scala',
      'clj': 'Clojure',
      'hs': 'Haskell',
      'ml': 'OCaml',
      'r': 'R',
      'jl': 'Julia',
      'dart': 'Dart',
      'elm': 'Elm'
    };
    
    return extensionMap[extension] || null;
  }

  /**
   * Get list of files in repository
   * @param {string} repoPath - Repository path
   * @returns {Promise<Array<string>>} File list
   */
  async getFileList(repoPath) {
    const files = [];
    
    try {
      const getAllFiles = async (dir) => {
        const items = await fileSystem.readdir(dir);
        
        for (const item of items) {
          if (item.startsWith('.')) continue;
          
          const itemPath = `${dir}/${item}`;
          const stats = await fileSystem.stat(itemPath);
          
          if (stats.isDirectory()) {
            await getAllFiles(itemPath);
          } else {
            files.push(itemPath.replace(repoPath + '/', ''));
          }
        }
      };

      await getAllFiles(repoPath);
      return files;
    } catch (error) {
      this.logger.warn('Failed to get file list', { 
        error: error.message 
      });
      return files;
    }
  }

  /**
   * Read README file if it exists
   * @param {string} repoPath - Repository path
   * @returns {Promise<string>} README content
   */
  async readReadme(repoPath) {
    const readmeFiles = ['README.md', 'README.txt', 'README', 'readme.md', 'readme.txt'];
    
    for (const filename of readmeFiles) {
      try {
        const readmePath = `${repoPath}/${filename}`;
        const exists = await fileSystem.exists(readmePath);
        
        if (exists) {
          const content = await fileSystem.readFile(readmePath, 'utf8');
          return content;
        }
      } catch (error) {
        // Continue to next file
      }
    }
    
    return '';
  }

  /**
   * Get git information
   * @param {string} repoPath - Repository path
   * @returns {Promise<Object>} Git information
   */
  async getGitInfo(repoPath) {
    const gitInfo = {
      branch: '',
      commits: 0,
      contributors: 0,
      lastCommit: null
    };

    try {
      gitInfo.branch = await gitManager.getCurrentBranch(repoPath);
      
      const commitHistory = await gitManager.getCommitHistory(repoPath, { limit: 100 });
      gitInfo.commits = commitHistory.length;
      
      if (commitHistory.length > 0) {
        gitInfo.lastCommit = commitHistory[0];
        
        // Count unique contributors
        const contributors = new Set(commitHistory.map(commit => commit.author));
        gitInfo.contributors = contributors.size;
      }

      return gitInfo;
    } catch (error) {
      this.logger.warn('Failed to get git info', { 
        error: error.message 
      });
      return gitInfo;
    }
  }

  /**
   * Analyze dependencies
   * @param {string} repoPath - Repository path
   * @returns {Promise<Object>} Dependencies analysis
   */
  async analyzeDependencies(repoPath) {
    const dependencies = {
      packageJson: {},
      requirements: {},
      gemfile: {},
      other: {}
    };

    try {
      // Check for package.json
      const packageJsonPath = `${repoPath}/package.json`;
      if (await fileSystem.exists(packageJsonPath)) {
        const packageJson = await fileSystem.readJson(packageJsonPath);
        dependencies.packageJson = {
          dependencies: Object.keys(packageJson.dependencies || {}),
          devDependencies: Object.keys(packageJson.devDependencies || {})
        };
      }

      // Check for requirements.txt
      const requirementsPath = `${repoPath}/requirements.txt`;
      if (await fileSystem.exists(requirementsPath)) {
        const content = await fileSystem.readFile(requirementsPath, 'utf8');
        dependencies.requirements = content.split('\n')
          .filter(line => line.trim() && !line.startsWith('#'))
          .map(line => line.split('==')[0].split('>=')[0].split('<=')[0].trim());
      }

      return dependencies;
    } catch (error) {
      this.logger.warn('Failed to analyze dependencies', { 
        error: error.message 
      });
      return dependencies;
    }
  }

  /**
   * Detect code patterns
   * @param {string} repoPath - Repository path
   * @returns {Promise<Array<string>>} Detected patterns
   */
  async detectCodePatterns(repoPath) {
    const patterns = [];

    try {
      const files = await this.getFileList(repoPath);
      
      // Check for common patterns
      if (files.some(f => f.includes('test') || f.includes('spec'))) {
        patterns.push('has-tests');
      }
      
      if (files.some(f => f.includes('config') || f.includes('settings'))) {
        patterns.push('configurable');
      }
      
      if (files.some(f => f.includes('api') || f.includes('server'))) {
        patterns.push('api-service');
      }
      
      if (files.some(f => f.includes('component') || f.includes('view'))) {
        patterns.push('ui-components');
      }
      
      if (files.some(f => f.includes('model') || f.includes('entity'))) {
        patterns.push('data-models');
      }

      return patterns;
    } catch (error) {
      this.logger.warn('Failed to detect code patterns', { 
        error: error.message 
      });
      return patterns;
    }
  }

  /**
   * Assess repository complexity
   * @param {Object} analysis - Repository analysis
   * @returns {string} Complexity level
   */
  assessRepositoryComplexity(analysis) {
    let complexityScore = 0;

    // Language diversity
    const languageCount = Object.keys(analysis.languages).length;
    if (languageCount > 3) complexityScore += 2;
    else if (languageCount > 1) complexityScore += 1;

    // File count
    const fileCount = analysis.files.length;
    if (fileCount > 100) complexityScore += 2;
    else if (fileCount > 20) complexityScore += 1;

    // Directory structure
    if (analysis.structure.directories.length > 10) complexityScore += 2;
    else if (analysis.structure.directories.length > 5) complexityScore += 1;

    // Dependencies
    const depCount = (analysis.dependencies.packageJson.dependencies || []).length +
                    (analysis.dependencies.requirements || []).length;
    if (depCount > 20) complexityScore += 2;
    else if (depCount > 5) complexityScore += 1;

    // Code patterns
    if (analysis.codePatterns.length > 3) complexityScore += 1;

    if (complexityScore >= 6) return 'high';
    if (complexityScore >= 3) return 'medium';
    return 'low';
  }

  /**
   * Generate repository summary using LLM
   * @param {Object} analysis - Repository analysis
   * @returns {Promise<string>} Repository summary
   */
  async generateRepositorySummary(analysis) {
    try {
      const prompt = this.buildRepositorySummaryPrompt(analysis);
      const summary = await this.callLLM(prompt, { maxTokens: 500 });
      
      return summary;
    } catch (error) {
      this.logger.warn('Failed to generate LLM summary, using fallback', { 
        error: error.message 
      });
      return this.generateFallbackSummary(analysis);
    }
  }

  /**
   * Build prompt for repository summary generation
   * @param {Object} analysis - Repository analysis
   * @returns {string} Summary prompt
   */
  buildRepositorySummaryPrompt(analysis) {
    const languages = Object.keys(analysis.languages).join(', ');
    const patterns = analysis.codePatterns.join(', ');
    const directories = analysis.structure.directories.slice(0, 10).join(', ');

    return `Analyze this repository and provide a concise summary:

Repository Information:
- Languages: ${languages}
- File count: ${analysis.files.length}
- Directories: ${directories}
- Code patterns: ${patterns}
- Complexity: ${analysis.complexity}
- Git commits: ${analysis.gitInfo.commits}

README excerpt:
${analysis.readme.substring(0, 500)}

Provide a 2-3 sentence summary describing what this repository does, its main technologies, and its purpose.`;
  }

  /**
   * Generate fallback summary without LLM
   * @param {Object} analysis - Repository analysis
   * @returns {string} Fallback summary
   */
  generateFallbackSummary(analysis) {
    const primaryLanguage = Object.keys(analysis.languages)[0] || 'Unknown';
    const fileCount = analysis.files.length;
    const complexity = analysis.complexity;

    return `This is a ${primaryLanguage} project with ${fileCount} files and ${complexity} complexity. ` +
           `It includes ${analysis.structure.directories.length} main directories and ` +
           `follows ${analysis.codePatterns.length} common code patterns.`;
  }

  /**
   * Generate prompts based on repository analysis
   * @param {Object} options - Generation options
   * @returns {Promise<Array<Object>>} Generated prompts
   */
  async generatePrompts(options) {
    const {
      repoSummary,
      repoAnalysis,
      numPrompts,
      topics,
      complexity,
      promptTypes,
      includeContext
    } = options;

    const prompts = [];

    try {
      for (let i = 0; i < numPrompts; i++) {
        const promptType = promptTypes[i % promptTypes.length];
        const topic = topics.length > 0 ? topics[i % topics.length] : null;
        
        const prompt = await this.generateSinglePrompt({
          repoSummary,
          repoAnalysis,
          promptType,
          topic,
          complexity,
          includeContext
        });

        prompts.push(prompt);
      }

      return prompts;
    } catch (error) {
      this.logger.error('Failed to generate prompts', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Generate a single prompt
   * @param {Object} options - Prompt options
   * @returns {Promise<Object>} Generated prompt
   */
  async generateSinglePrompt(options) {
    const {
      repoSummary,
      repoAnalysis,
      promptType,
      topic,
      complexity,
      includeContext
    } = options;

    try {
      const generationPrompt = this.buildPromptGenerationPrompt(options);
      const generatedText = await this.callLLM(generationPrompt, { maxTokens: 800 });
      
      return {
        id: this.generatePromptId(),
        type: promptType,
        topic: topic,
        complexity: complexity,
        text: generatedText.trim(),
        context: includeContext ? {
          repoSummary,
          primaryLanguage: Object.keys(repoAnalysis.languages)[0],
          fileCount: repoAnalysis.files.length,
          patterns: repoAnalysis.codePatterns
        } : null,
        metadata: {
          generated: new Date().toISOString(),
          generator: 'PromptGenerator',
          version: '1.0.0'
        }
      };
    } catch (error) {
      this.logger.warn('Failed to generate LLM prompt, using template', { 
        error: error.message 
      });
      return this.generateTemplatePrompt(options);
    }
  }

  /**
   * Build prompt for prompt generation
   * @param {Object} options - Prompt options
   * @returns {string} Generation prompt
   */
  buildPromptGenerationPrompt(options) {
    const {
      repoSummary,
      repoAnalysis,
      promptType,
      topic,
      complexity
    } = options;

    const primaryLanguage = Object.keys(repoAnalysis.languages)[0] || 'the primary language';

    let basePrompt = `Generate a coding task prompt for an AI assistant based on this repository:

Repository Summary: ${repoSummary}

Requirements:
- Task type: ${promptType}
- Complexity: ${complexity}
- Primary language: ${primaryLanguage}`;

    if (topic) {
      basePrompt += `\n- Focus topic: ${topic}`;
    }

    basePrompt += `\n\nGenerate a clear, specific prompt that asks the AI to work with this codebase. The prompt should:
1. Be specific and actionable
2. Match the ${complexity} complexity level
3. Be appropriate for the ${promptType} task type
4. Reference the codebase context appropriately

Prompt:`;

    return basePrompt;
  }

  /**
   * Generate template-based prompt as fallback
   * @param {Object} options - Prompt options
   * @returns {Object} Template prompt
   */
  generateTemplatePrompt(options) {
    const {
      repoSummary,
      repoAnalysis,
      promptType,
      topic,
      complexity
    } = options;

    const templates = {
      implementation: `Implement a new ${topic || 'feature'} for this ${Object.keys(repoAnalysis.languages)[0]} project. The implementation should follow the existing code patterns and architecture.`,
      debugging: `Debug and fix an issue in this ${Object.keys(repoAnalysis.languages)[0]} codebase related to ${topic || 'functionality'}. Identify the problem and provide a solution.`,
      enhancement: `Enhance the existing ${topic || 'functionality'} in this project by adding new capabilities while maintaining backward compatibility.`,
      refactoring: `Refactor the ${topic || 'code structure'} in this project to improve maintainability and performance while preserving functionality.`
    };

    return {
      id: this.generatePromptId(),
      type: promptType,
      topic: topic,
      complexity: complexity,
      text: templates[promptType] || templates.implementation,
      context: {
        repoSummary,
        primaryLanguage: Object.keys(repoAnalysis.languages)[0],
        fileCount: repoAnalysis.files.length,
        patterns: repoAnalysis.codePatterns
      },
      metadata: {
        generated: new Date().toISOString(),
        generator: 'PromptGenerator',
        version: '1.0.0',
        fallback: true
      }
    };
  }

  /**
   * Generate unique prompt ID
   * @returns {string} Prompt ID
   */
  generatePromptId() {
    return `prompt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Call LLM API
   * @param {string} prompt - Prompt text
   * @param {Object} options - API options
   * @returns {Promise<string>} LLM response
   */
  async callLLM(prompt, options = {}) {
    const {
      maxTokens = this.config.maxTokens,
      temperature = this.config.temperature
    } = options;

    let lastError;
    
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        this.logger.debug(`LLM API call attempt ${attempt}`, {
          provider: this.config.llmProvider,
          model: this.config.llmModel
        });

        let response;
        
        switch (this.config.llmProvider) {
          case 'openai':
            response = await this.callOpenAI(prompt, { maxTokens, temperature });
            break;
          case 'anthropic':
            response = await this.callAnthropic(prompt, { maxTokens, temperature });
            break;
          case 'local':
            response = await this.callLocalLLM(prompt, { maxTokens, temperature });
            break;
          default:
            throw new Error(`Unsupported LLM provider: ${this.config.llmProvider}`);
        }

        return response;
      } catch (error) {
        lastError = error;
        this.logger.warn(`LLM API call attempt ${attempt} failed`, {
          error: error.message
        });
        
        if (attempt < this.config.maxRetries) {
          await this.delay(this.config.retryDelay * attempt);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Call OpenAI API
   * @param {string} prompt - Prompt text
   * @param {Object} options - API options
   * @returns {Promise<string>} Response text
   */
  async callOpenAI(prompt, options) {
    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: this.config.llmModel,
        messages: [{ role: 'user', content: prompt }],
        max_tokens: options.maxTokens,
        temperature: options.temperature
      },
      {
        headers: {
          'Authorization': `Bearer ${this.config.llmApiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: this.config.timeout
      }
    );

    return response.data.choices[0].message.content;
  }

  /**
   * Call Anthropic API
   * @param {string} prompt - Prompt text
   * @param {Object} options - API options
   * @returns {Promise<string>} Response text
   */
  async callAnthropic(prompt, options) {
    const response = await axios.post(
      'https://api.anthropic.com/v1/messages',
      {
        model: this.config.llmModel,
        max_tokens: options.maxTokens,
        temperature: options.temperature,
        messages: [{ role: 'user', content: prompt }]
      },
      {
        headers: {
          'x-api-key': this.config.llmApiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        timeout: this.config.timeout
      }
    );

    return response.data.content[0].text;
  }

  /**
   * Call local LLM endpoint
   * @param {string} prompt - Prompt text
   * @param {Object} options - API options
   * @returns {Promise<string>} Response text
   */
  async callLocalLLM(prompt, options) {
    const response = await axios.post(
      this.config.llmLocalEndpoint,
      {
        prompt: prompt,
        max_tokens: options.maxTokens,
        temperature: options.temperature,
        model: this.config.llmModel
      },
      {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: this.config.timeout
      }
    );

    return response.data.response || response.data.text || response.data.content;
  }

  /**
   * Delay execution
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise<void>}
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate prompts from topics list
   * @param {Array<string>} topics - List of topics
   * @param {Object} options - Generation options
   * @returns {Promise<Array<Object>>} Generated prompts
   */
  async generatePromptsFromTopics(topics, options = {}) {
    const {
      complexity = 'medium',
      promptTypes = ['implementation', 'debugging', 'enhancement'],
      context = ''
    } = options;

    const prompts = [];

    for (const topic of topics) {
      for (const promptType of promptTypes) {
        const prompt = await this.generateTopicPrompt({
          topic,
          promptType,
          complexity,
          context
        });
        
        prompts.push(prompt);
      }
    }

    return prompts;
  }

  /**
   * Generate prompt for specific topic
   * @param {Object} options - Topic prompt options
   * @returns {Promise<Object>} Generated prompt
   */
  async generateTopicPrompt(options) {
    const { topic, promptType, complexity, context } = options;

    try {
      const generationPrompt = `Generate a ${complexity} complexity ${promptType} task prompt about ${topic}.

${context ? `Context: ${context}` : ''}

The prompt should be:
1. Clear and specific
2. Appropriate for the ${complexity} complexity level
3. Focused on ${promptType} tasks
4. Related to ${topic}

Prompt:`;

      const generatedText = await this.callLLM(generationPrompt, { maxTokens: 400 });
      
      return {
        id: this.generatePromptId(),
        type: promptType,
        topic: topic,
        complexity: complexity,
        text: generatedText.trim(),
        context: context || null,
        metadata: {
          generated: new Date().toISOString(),
          generator: 'PromptGenerator',
          version: '1.0.0'
        }
      };
    } catch (error) {
      this.logger.warn('Failed to generate topic prompt, using template', {
        error: error.message
      });
      
      return {
        id: this.generatePromptId(),
        type: promptType,
        topic: topic,
        complexity: complexity,
        text: `Create a ${promptType} solution for ${topic} with ${complexity} complexity.`,
        context: context || null,
        metadata: {
          generated: new Date().toISOString(),
          generator: 'PromptGenerator',
          version: '1.0.0',
          fallback: true
        }
      };
    }
  }

  /**
   * Save prompts to file
   * @param {Array<Object>} prompts - Prompts to save
   * @param {string} filename - Output filename
   * @returns {Promise<void>}
   */
  async savePrompts(prompts, filename) {
    try {
      await fileSystem.writeJson(filename, {
        prompts,
        metadata: {
          generated: new Date().toISOString(),
          count: prompts.length,
          generator: 'PromptGenerator',
          version: '1.0.0'
        }
      });

      this.logger.info('Prompts saved successfully', {
        filename,
        count: prompts.length
      });
    } catch (error) {
      this.logger.error('Failed to save prompts', {
        error: error.message,
        filename
      });
      throw error;
    }
  }

  /**
   * Load prompts from file
   * @param {string} filename - Input filename
   * @returns {Promise<Array<Object>>} Loaded prompts
   */
  async loadPrompts(filename) {
    try {
      const data = await fileSystem.readJson(filename);
      
      this.logger.info('Prompts loaded successfully', {
        filename,
        count: data.prompts.length
      });

      return data.prompts;
    } catch (error) {
      this.logger.error('Failed to load prompts', {
        error: error.message,
        filename
      });
      throw error;
    }
  }
}

// Create default instance
const promptGenerator = new PromptGenerator();

module.exports = { PromptGenerator, promptGenerator };
