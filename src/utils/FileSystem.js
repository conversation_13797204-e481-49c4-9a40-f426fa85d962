const fs = require('fs-extra');
const path = require('path');
const { logger } = require('./Logger');

/**
 * FileSystem utility - Enhanced file operations with logging and error handling
 */
class FileSystem {
  constructor(options = {}) {
    this.options = {
      logger: logger,
      ...options
    };
  }

  /**
   * Ensure directory exists, create if it doesn't
   * @param {string} dirPath - Directory path
   * @returns {Promise<boolean>} True if directory was created, false if it already existed
   */
  async ensureDir(dirPath) {
    try {
      const exists = await fs.pathExists(dirPath);
      if (!exists) {
        await fs.ensureDir(dirPath);
        this.options.logger.debug('Directory created', { path: dirPath });
        return true;
      }
      return false;
    } catch (error) {
      this.options.logger.error('Failed to ensure directory', { 
        path: dirPath, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Read file with error handling and logging
   * @param {string} filePath - File path
   * @param {string} encoding - File encoding (default: utf8)
   * @returns {Promise<string>} File contents
   */
  async readFile(filePath, encoding = 'utf8') {
    try {
      const content = await fs.readFile(filePath, encoding);
      this.options.logger.debug('File read successfully', { 
        path: filePath, 
        size: content.length 
      });
      return content;
    } catch (error) {
      this.options.logger.error('Failed to read file', { 
        path: filePath, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Write file with error handling and logging
   * @param {string} filePath - File path
   * @param {string} content - File content
   * @param {string} encoding - File encoding (default: utf8)
   */
  async writeFile(filePath, content, encoding = 'utf8') {
    try {
      await this.ensureDir(path.dirname(filePath));
      await fs.writeFile(filePath, content, encoding);
      this.options.logger.debug('File written successfully', { 
        path: filePath, 
        size: content.length 
      });
    } catch (error) {
      this.options.logger.error('Failed to write file', { 
        path: filePath, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Append to file with error handling and logging
   * @param {string} filePath - File path
   * @param {string} content - Content to append
   * @param {string} encoding - File encoding (default: utf8)
   */
  async appendFile(filePath, content, encoding = 'utf8') {
    try {
      await this.ensureDir(path.dirname(filePath));
      await fs.appendFile(filePath, content, encoding);
      this.options.logger.debug('Content appended to file', { 
        path: filePath, 
        size: content.length 
      });
    } catch (error) {
      this.options.logger.error('Failed to append to file', { 
        path: filePath, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Copy file or directory
   * @param {string} src - Source path
   * @param {string} dest - Destination path
   * @param {Object} options - Copy options
   */
  async copy(src, dest, options = {}) {
    try {
      await this.ensureDir(path.dirname(dest));
      await fs.copy(src, dest, options);
      this.options.logger.debug('Copy completed', { 
        src, 
        dest, 
        options 
      });
    } catch (error) {
      this.options.logger.error('Failed to copy', { 
        src, 
        dest, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Move file or directory
   * @param {string} src - Source path
   * @param {string} dest - Destination path
   */
  async move(src, dest) {
    try {
      await this.ensureDir(path.dirname(dest));
      await fs.move(src, dest);
      this.options.logger.debug('Move completed', { src, dest });
    } catch (error) {
      this.options.logger.error('Failed to move', { 
        src, 
        dest, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Remove file or directory
   * @param {string} targetPath - Path to remove
   */
  async remove(targetPath) {
    try {
      const exists = await fs.pathExists(targetPath);
      if (exists) {
        await fs.remove(targetPath);
        this.options.logger.debug('Removed successfully', { path: targetPath });
      } else {
        this.options.logger.debug('Path does not exist, nothing to remove', { 
          path: targetPath 
        });
      }
    } catch (error) {
      this.options.logger.error('Failed to remove', { 
        path: targetPath, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Check if path exists
   * @param {string} targetPath - Path to check
   * @returns {Promise<boolean>} True if path exists
   */
  async exists(targetPath) {
    try {
      return await fs.pathExists(targetPath);
    } catch (error) {
      this.options.logger.error('Failed to check path existence', { 
        path: targetPath, 
        error: error.message 
      });
      return false;
    }
  }

  /**
   * Get file/directory stats
   * @param {string} targetPath - Path to stat
   * @returns {Promise<Object>} File stats
   */
  async stat(targetPath) {
    try {
      const stats = await fs.stat(targetPath);
      this.options.logger.debug('Stats retrieved', { 
        path: targetPath, 
        size: stats.size,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory()
      });
      return stats;
    } catch (error) {
      this.options.logger.error('Failed to get stats', { 
        path: targetPath, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * List directory contents
   * @param {string} dirPath - Directory path
   * @returns {Promise<Array>} Array of file/directory names
   */
  async readdir(dirPath) {
    try {
      const items = await fs.readdir(dirPath);
      this.options.logger.debug('Directory read', { 
        path: dirPath, 
        count: items.length 
      });
      return items;
    } catch (error) {
      this.options.logger.error('Failed to read directory', { 
        path: dirPath, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Read JSON file
   * @param {string} filePath - JSON file path
   * @returns {Promise<Object>} Parsed JSON object
   */
  async readJson(filePath) {
    try {
      const data = await fs.readJson(filePath);
      this.options.logger.debug('JSON file read', { path: filePath });
      return data;
    } catch (error) {
      this.options.logger.error('Failed to read JSON file', { 
        path: filePath, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Write JSON file
   * @param {string} filePath - JSON file path
   * @param {Object} data - Data to write
   * @param {Object} options - Write options
   */
  async writeJson(filePath, data, options = { spaces: 2 }) {
    try {
      await this.ensureDir(path.dirname(filePath));
      await fs.writeJson(filePath, data, options);
      this.options.logger.debug('JSON file written', { path: filePath });
    } catch (error) {
      this.options.logger.error('Failed to write JSON file', { 
        path: filePath, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Create temporary file
   * @param {string} prefix - File prefix
   * @param {string} suffix - File suffix
   * @returns {Promise<string>} Temporary file path
   */
  async createTempFile(prefix = 'augbench-', suffix = '.tmp') {
    try {
      const tempDir = await fs.mkdtemp(path.join(require('os').tmpdir(), prefix));
      const tempFile = path.join(tempDir, `file${suffix}`);
      this.options.logger.debug('Temporary file created', { path: tempFile });
      return tempFile;
    } catch (error) {
      this.options.logger.error('Failed to create temporary file', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Create temporary directory
   * @param {string} prefix - Directory prefix
   * @returns {Promise<string>} Temporary directory path
   */
  async createTempDir(prefix = 'augbench-') {
    try {
      const tempDir = await fs.mkdtemp(path.join(require('os').tmpdir(), prefix));
      this.options.logger.debug('Temporary directory created', { path: tempDir });
      return tempDir;
    } catch (error) {
      this.options.logger.error('Failed to create temporary directory', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Get directory size recursively
   * @param {string} dirPath - Directory path
   * @returns {Promise<number>} Size in bytes
   */
  async getDirectorySize(dirPath) {
    try {
      let totalSize = 0;
      
      const items = await this.readdir(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stats = await this.stat(itemPath);
        
        if (stats.isDirectory()) {
          totalSize += await this.getDirectorySize(itemPath);
        } else {
          totalSize += stats.size;
        }
      }
      
      this.options.logger.debug('Directory size calculated', { 
        path: dirPath, 
        size: totalSize 
      });
      
      return totalSize;
    } catch (error) {
      this.options.logger.error('Failed to calculate directory size', { 
        path: dirPath, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Clean directory (remove all contents but keep directory)
   * @param {string} dirPath - Directory path
   */
  async cleanDirectory(dirPath) {
    try {
      const exists = await this.exists(dirPath);
      if (!exists) {
        await this.ensureDir(dirPath);
        return;
      }

      const items = await this.readdir(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        await this.remove(itemPath);
      }
      
      this.options.logger.debug('Directory cleaned', { 
        path: dirPath, 
        itemsRemoved: items.length 
      });
    } catch (error) {
      this.options.logger.error('Failed to clean directory', { 
        path: dirPath, 
        error: error.message 
      });
      throw error;
    }
  }
}

// Create default instance
const fileSystem = new FileSystem();

module.exports = { FileSystem, fileSystem };
