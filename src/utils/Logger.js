const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * Logger utility - Provides timestamped logging with multiple levels and outputs
 */
class Logger {
  constructor(options = {}) {
    this.options = {
      level: 'info', // debug, info, warn, error
      console: true,
      file: null,
      timestamp: true,
      colors: true,
      ...options
    };

    this.levels = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };

    this.colors = {
      debug: chalk.gray,
      info: chalk.blue,
      warn: chalk.yellow,
      error: chalk.red
    };

    // Ensure log file directory exists if file logging is enabled
    if (this.options.file) {
      this.ensureLogDirectory();
    }
  }

  async ensureLogDirectory() {
    const logDir = path.dirname(this.options.file);
    await fs.ensureDir(logDir);
  }

  shouldLog(level) {
    return this.levels[level] >= this.levels[this.options.level];
  }

  formatMessage(level, message, data = {}) {
    const timestamp = this.options.timestamp ? new Date().toISOString() : '';
    const levelStr = level.toUpperCase().padEnd(5);
    
    let formatted = '';
    
    if (timestamp) {
      formatted += `[${timestamp}] `;
    }
    
    formatted += `[${levelStr}] ${message}`;
    
    if (data && Object.keys(data).length > 0) {
      formatted += ` ${JSON.stringify(data)}`;
    }
    
    return formatted;
  }

  formatConsoleMessage(level, message, data = {}) {
    const timestamp = this.options.timestamp ? chalk.gray(`[${new Date().toISOString()}]`) : '';
    const levelColor = this.options.colors ? this.colors[level] : (text) => text;
    const levelStr = levelColor(`[${level.toUpperCase().padEnd(5)}]`);
    
    let formatted = '';
    
    if (timestamp) {
      formatted += `${timestamp} `;
    }
    
    formatted += `${levelStr} ${message}`;
    
    if (data && Object.keys(data).length > 0) {
      formatted += ` ${chalk.gray(JSON.stringify(data, null, 2))}`;
    }
    
    return formatted;
  }

  async writeToFile(message) {
    if (!this.options.file) return;
    
    try {
      await fs.appendFile(this.options.file, message + '\n', 'utf8');
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    }
  }

  async log(level, message, data = {}) {
    if (!this.shouldLog(level)) return;

    // Console output
    if (this.options.console) {
      const consoleMessage = this.formatConsoleMessage(level, message, data);
      console.log(consoleMessage);
    }

    // File output
    if (this.options.file) {
      const fileMessage = this.formatMessage(level, message, data);
      await this.writeToFile(fileMessage);
    }
  }

  debug(message, data = {}) {
    return this.log('debug', message, data);
  }

  info(message, data = {}) {
    return this.log('info', message, data);
  }

  warn(message, data = {}) {
    return this.log('warn', message, data);
  }

  error(message, data = {}) {
    return this.log('error', message, data);
  }

  // Convenience methods for common logging patterns
  async logExecution(operation, startTime, success = true, data = {}) {
    const duration = Date.now() - startTime;
    const level = success ? 'info' : 'error';
    const status = success ? 'completed' : 'failed';
    
    await this.log(level, `${operation} ${status}`, {
      duration: `${duration}ms`,
      ...data
    });
  }

  async logError(error, context = {}) {
    await this.error(error.message, {
      stack: error.stack,
      code: error.code,
      ...context
    });
  }

  async logMetric(metricName, value, unit = '', context = {}) {
    await this.info(`Metric: ${metricName}`, {
      value,
      unit,
      ...context
    });
  }

  // Create a child logger with additional context
  child(context = {}) {
    return new ChildLogger(this, context);
  }

  // Set log level
  setLevel(level) {
    if (this.levels[level] !== undefined) {
      this.options.level = level;
    } else {
      throw new Error(`Invalid log level: ${level}. Valid levels: ${Object.keys(this.levels).join(', ')}`);
    }
  }

  // Enable/disable console output
  setConsole(enabled) {
    this.options.console = enabled;
  }

  // Set log file
  async setFile(filePath) {
    this.options.file = filePath;
    if (filePath) {
      await this.ensureLogDirectory();
    }
  }
}

/**
 * Child Logger - Adds context to all log messages
 */
class ChildLogger {
  constructor(parent, context) {
    this.parent = parent;
    this.context = context;
  }

  async log(level, message, data = {}) {
    const mergedData = { ...this.context, ...data };
    return this.parent.log(level, message, mergedData);
  }

  debug(message, data = {}) {
    return this.log('debug', message, data);
  }

  info(message, data = {}) {
    return this.log('info', message, data);
  }

  warn(message, data = {}) {
    return this.log('warn', message, data);
  }

  error(message, data = {}) {
    return this.log('error', message, data);
  }

  async logExecution(operation, startTime, success = true, data = {}) {
    const duration = Date.now() - startTime;
    const level = success ? 'info' : 'error';
    const status = success ? 'completed' : 'failed';
    
    await this.log(level, `${operation} ${status}`, {
      duration: `${duration}ms`,
      ...data
    });
  }

  async logError(error, context = {}) {
    await this.error(error.message, {
      stack: error.stack,
      code: error.code,
      ...context
    });
  }

  child(additionalContext = {}) {
    return new ChildLogger(this.parent, { ...this.context, ...additionalContext });
  }
}

// Create default logger instance
const defaultLogger = new Logger({
  level: process.env.LOG_LEVEL || 'info',
  console: true,
  file: process.env.LOG_FILE || null,
  colors: process.stdout.isTTY
});

module.exports = { 
  Logger, 
  ChildLogger,
  logger: defaultLogger 
};
