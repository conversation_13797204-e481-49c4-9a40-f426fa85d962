const path = require('path');
const { fileSystem } = require('./FileSystem');
const { logger } = require('./Logger');

/**
 * Results Storage - Manages JSON output and results persistence
 */
class ResultsStorage {
  constructor(options = {}) {
    this.options = {
      logger: logger,
      resultsDir: './results',
      ...options
    };
  }

  /**
   * Initialize results storage
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      await fileSystem.ensureDir(this.options.resultsDir);
      this.options.logger.debug('Results storage initialized', { 
        resultsDir: this.options.resultsDir 
      });
    } catch (error) {
      this.options.logger.error('Failed to initialize results storage', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Create results metadata
   * @param {Object} settings - Benchmark settings
   * @returns {Object} Metadata object
   */
  createMetadata(settings) {
    return {
      timestamp: new Date().toISOString(),
      mode: settings.mode,
      version: '1.0.0',
      totalRuns: 0, // Will be updated as results are added
      platform: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version
      },
      settings: {
        agents: settings.agents,
        metrics: settings.metrics,
        runs_per_prompt: settings.runs_per_prompt,
        parallel_agents: settings.parallel_agents,
        repo_url: settings.repo_url,
        repo_path: settings.repo_path,
        branch: settings.branch
      }
    };
  }

  /**
   * Create new results file
   * @param {string} filename - Results filename (without extension)
   * @param {Object} settings - Benchmark settings
   * @returns {Promise<Object>} Results object
   */
  async createResults(filename, settings) {
    try {
      await this.initialize();
      
      const results = {
        metadata: this.createMetadata(settings),
        results: []
      };

      const filePath = this.getResultsPath(filename);
      await fileSystem.writeJson(filePath, results);
      
      this.options.logger.info('Results file created', { 
        filename, 
        path: filePath 
      });
      
      return results;
    } catch (error) {
      this.options.logger.error('Failed to create results file', { 
        filename, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Load existing results file
   * @param {string} filename - Results filename (without extension)
   * @returns {Promise<Object>} Results object
   */
  async loadResults(filename) {
    try {
      const filePath = this.getResultsPath(filename);
      const exists = await fileSystem.exists(filePath);
      
      if (!exists) {
        throw new Error(`Results file not found: ${filePath}`);
      }

      const results = await fileSystem.readJson(filePath);
      
      this.options.logger.debug('Results file loaded', { 
        filename, 
        resultCount: results.results.length 
      });
      
      return results;
    } catch (error) {
      this.options.logger.error('Failed to load results file', { 
        filename, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Save results to file
   * @param {string} filename - Results filename (without extension)
   * @param {Object} results - Results object
   * @returns {Promise<void>}
   */
  async saveResults(filename, results) {
    try {
      // Update total runs count
      results.metadata.totalRuns = results.results.reduce((total, result) => {
        return total + (result.runs ? result.runs.length : 0);
      }, 0);

      const filePath = this.getResultsPath(filename);
      await fileSystem.writeJson(filePath, results);
      
      this.options.logger.debug('Results file saved', { 
        filename, 
        totalRuns: results.metadata.totalRuns 
      });
    } catch (error) {
      this.options.logger.error('Failed to save results file', { 
        filename, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Add result entry to results
   * @param {Object} results - Results object
   * @param {string} prompt - Prompt identifier
   * @param {string} agent - Agent name
   * @param {Object} runResult - Run result data
   * @returns {void}
   */
  addResult(results, prompt, agent, runResult) {
    try {
      // Find existing entry for this prompt-agent combination
      let entry = results.results.find(r => r.prompt === prompt && r.agent === agent);
      
      if (!entry) {
        entry = {
          prompt,
          agent,
          runs: []
        };
        results.results.push(entry);
      }

      // Add run result
      entry.runs.push({
        run_id: entry.runs.length + 1,
        ...runResult
      });

      this.options.logger.debug('Result added', { 
        prompt, 
        agent, 
        runId: entry.runs.length 
      });
    } catch (error) {
      this.options.logger.error('Failed to add result', { 
        prompt, 
        agent, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Get results path
   * @param {string} filename - Filename without extension
   * @returns {string} Full file path
   */
  getResultsPath(filename) {
    return path.join(this.options.resultsDir, `${filename}.json`);
  }

  /**
   * Get chart path
   * @param {string} filename - Base filename
   * @param {string} metric - Metric name
   * @returns {string} Chart file path
   */
  getChartPath(filename, metric) {
    return path.join(this.options.resultsDir, `${filename}_${metric}.png`);
  }

  /**
   * Export results summary
   * @param {Object} results - Results object
   * @returns {Object} Summary statistics
   */
  exportSummary(results) {
    try {
      const summary = {
        metadata: results.metadata,
        summary: {
          totalPrompts: 0,
          totalAgents: 0,
          totalRuns: results.metadata.totalRuns,
          agents: {},
          metrics: {}
        }
      };

      const prompts = new Set();
      const agents = new Set();
      const metrics = new Set();

      // Analyze results
      for (const result of results.results) {
        prompts.add(result.prompt);
        agents.add(result.agent);

        if (!summary.summary.agents[result.agent]) {
          summary.summary.agents[result.agent] = {
            totalRuns: 0,
            successfulRuns: 0,
            failedRuns: 0
          };
        }

        for (const run of result.runs) {
          summary.summary.agents[result.agent].totalRuns++;
          
          if (run.error) {
            summary.summary.agents[result.agent].failedRuns++;
          } else {
            summary.summary.agents[result.agent].successfulRuns++;
          }

          // Collect metrics
          Object.keys(run).forEach(key => {
            if (key !== 'run_id' && key !== 'error' && typeof run[key] === 'number') {
              metrics.add(key);
              
              if (!summary.summary.metrics[key]) {
                summary.summary.metrics[key] = {
                  count: 0,
                  sum: 0,
                  min: Infinity,
                  max: -Infinity,
                  avg: 0
                };
              }

              const value = run[key];
              summary.summary.metrics[key].count++;
              summary.summary.metrics[key].sum += value;
              summary.summary.metrics[key].min = Math.min(summary.summary.metrics[key].min, value);
              summary.summary.metrics[key].max = Math.max(summary.summary.metrics[key].max, value);
            }
          });
        }
      }

      // Calculate averages
      Object.keys(summary.summary.metrics).forEach(metric => {
        const metricData = summary.summary.metrics[metric];
        metricData.avg = metricData.sum / metricData.count;
      });

      summary.summary.totalPrompts = prompts.size;
      summary.summary.totalAgents = agents.size;

      this.options.logger.debug('Summary exported', { 
        prompts: summary.summary.totalPrompts,
        agents: summary.summary.totalAgents,
        runs: summary.summary.totalRuns
      });

      return summary;
    } catch (error) {
      this.options.logger.error('Failed to export summary', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Get metric data for charting
   * @param {Object} results - Results object
   * @param {string} metric - Metric name
   * @returns {Object} Chart data
   */
  getMetricChartData(results, metric) {
    try {
      const chartData = {
        metric,
        prompts: [],
        agents: [],
        data: {}
      };

      const prompts = new Set();
      const agents = new Set();

      // Collect prompts and agents
      for (const result of results.results) {
        prompts.add(result.prompt);
        agents.add(result.agent);
      }

      chartData.prompts = Array.from(prompts).sort();
      chartData.agents = Array.from(agents).sort();

      // Initialize data structure
      for (const agent of chartData.agents) {
        chartData.data[agent] = {};
        for (const prompt of chartData.prompts) {
          chartData.data[agent][prompt] = [];
        }
      }

      // Populate data
      for (const result of results.results) {
        for (const run of result.runs) {
          if (run[metric] !== undefined && run[metric] !== null && run[metric] !== 'NA') {
            chartData.data[result.agent][result.prompt].push(run[metric]);
          }
        }
      }

      // Calculate averages
      for (const agent of chartData.agents) {
        for (const prompt of chartData.prompts) {
          const values = chartData.data[agent][prompt];
          if (values.length > 0) {
            chartData.data[agent][prompt] = values.reduce((sum, val) => sum + val, 0) / values.length;
          } else {
            chartData.data[agent][prompt] = null;
          }
        }
      }

      this.options.logger.debug('Chart data prepared', { 
        metric, 
        prompts: chartData.prompts.length,
        agents: chartData.agents.length
      });

      return chartData;
    } catch (error) {
      this.options.logger.error('Failed to prepare chart data', { 
        metric, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * List all results files
   * @returns {Promise<Array>} Array of results file names
   */
  async listResults() {
    try {
      await this.initialize();
      
      const files = await fileSystem.readdir(this.options.resultsDir);
      const jsonFiles = files
        .filter(file => file.endsWith('.json'))
        .map(file => file.replace('.json', ''));

      this.options.logger.debug('Results files listed', { 
        count: jsonFiles.length 
      });

      return jsonFiles;
    } catch (error) {
      this.options.logger.error('Failed to list results files', { 
        error: error.message 
      });
      throw error;
    }
  }
}

// Create default instance
const resultsStorage = new ResultsStorage();

module.exports = { ResultsStorage, resultsStorage };
