const Joi = require('joi');
const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');
const axios = require('axios');
require('dotenv').config();

/**
 * Settings Manager - Handles configuration loading and validation
 */
class SettingsManager {
  constructor() {
    this.settingsPath = path.join(process.cwd(), 'settings.json');
    this.schema = this.createValidationSchema();
  }

  createValidationSchema() {
    const agentConfigSchema = Joi.object({
      model: Joi.string().optional(),
      timeout: Joi.number().integer().min(1000).default(600000)
    }).unknown(true);

    const llmEvaluatorSchema = Joi.object({
      generate_prompts: Joi.boolean().default(true),
      prompt_topics: Joi.array().items(Joi.string()).when('generate_prompts', {
        is: true,
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    });

    const prRecreateSchema = Joi.object({
      num_prs: Joi.number().integer().min(1).max(50).required()
    });

    return Joi.object({
      // Common settings
      agents: Joi.array().items(
        Joi.string().valid('Augment CLI', 'Claude Code', 'Cursor CLI')
      ).min(1).required(),
      runs_per_prompt: Joi.number().integer().min(1).default(1),
      parallel_agents: Joi.boolean().default(false),
      output_filename: Joi.string().required(),
      stage_dir: Joi.string().default('./stage'),
      repo_url: Joi.string().uri().optional(),
      repo_path: Joi.string().optional(),
      branch: Joi.string().default('main'),

      // Metrics configuration
      metrics: Joi.array().items(
        Joi.string().valid(
          'response_time',
          'diff_metrics',
          'ast_similarity',
          'completeness',
          'technical_correctness',
          'functional_correctness',
          'clarity',
          'instruction_adherence'
        )
      ).min(1).required(),

      // Agent-specific configuration
      agent_config: Joi.object().pattern(
        Joi.string().valid('Augment CLI', 'Claude Code', 'Cursor CLI'),
        agentConfigSchema
      ).default({}),

      // Mode selection and mode-specific settings
      mode: Joi.string().valid('LLM_Evaluator', 'PR_Recreate').required(),
      LLM_Evaluator: llmEvaluatorSchema.when('mode', {
        is: 'LLM_Evaluator',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      PR_Recreate: prRecreateSchema.when('mode', {
        is: 'PR_Recreate',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    }).custom((value, helpers) => {
      // Custom validation rules
      
      // Check repo_url or repo_path is provided
      if (!value.repo_url && !value.repo_path) {
        return helpers.error('custom.repo_required');
      }

      // repo_path can only be used in LLM_Evaluator mode
      if (value.repo_path && value.mode !== 'LLM_Evaluator') {
        return helpers.error('custom.repo_path_llm_only');
      }

      // AST similarity only applies to PR_Recreate mode
      if (value.metrics.includes('ast_similarity') && value.mode !== 'PR_Recreate') {
        return helpers.error('custom.ast_similarity_pr_only');
      }

      return value;
    }).messages({
      'custom.repo_required': 'Either repo_url or repo_path must be provided',
      'custom.repo_path_llm_only': 'repo_path can only be used in LLM_Evaluator mode',
      'custom.ast_similarity_pr_only': 'ast_similarity metric only applies to PR_Recreate mode'
    });
  }

  async loadSettings() {
    try {
      // Check if settings.json exists
      if (!await fs.pathExists(this.settingsPath)) {
        throw new Error(`Settings file not found: ${this.settingsPath}\nPlease copy from settings.json.llm.example or settings.json.pr.example`);
      }

      // Load and parse settings
      const settingsContent = await fs.readFile(this.settingsPath, 'utf8');
      const settings = JSON.parse(settingsContent);

      // Validate settings
      const { error, value } = this.schema.validate(settings, {
        abortEarly: false,
        allowUnknown: false
      });

      if (error) {
        const errorMessages = error.details.map(detail => detail.message).join('\n');
        throw new Error(`Settings validation failed:\n${errorMessages}`);
      }

      return value;
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error(`Settings file not found: ${this.settingsPath}`);
      }
      if (error instanceof SyntaxError) {
        throw new Error(`Invalid JSON in settings file: ${error.message}`);
      }
      throw error;
    }
  }

  async validateEnvironment() {
    const results = {
      'System Requirements': [],
      'Git Configuration': [],
      'Agent Tools': [],
      'LLM Configuration': [],
      'Repository Access': [],
      'File System': []
    };

    // System Requirements
    results['System Requirements'].push(await this.checkNodeVersion());
    results['System Requirements'].push(await this.checkGitVersion());
    results['System Requirements'].push(await this.checkDiskSpace());

    // Load settings for further validation
    let settings;
    try {
      settings = await this.loadSettings();
      results['Git Configuration'].push({
        name: 'Settings file validation',
        valid: true,
        message: 'Settings loaded successfully'
      });
    } catch (error) {
      results['Git Configuration'].push({
        name: 'Settings file validation',
        valid: false,
        message: error.message
      });
      return results; // Can't continue without valid settings
    }

    // Git Configuration
    results['Git Configuration'].push(await this.checkGitConfig());

    // Agent Tools
    for (const agent of settings.agents) {
      results['Agent Tools'].push(await this.checkAgentTool(agent));
    }

    // LLM Configuration (if LLM-assessed metrics are used)
    const hasLLMMetrics = settings.metrics.some(metric => 
      ['completeness', 'technical_correctness', 'functional_correctness', 'clarity', 'instruction_adherence'].includes(metric)
    );
    
    if (hasLLMMetrics) {
      results['LLM Configuration'].push(await this.checkLLMConfiguration());
    }

    // Repository Access
    if (settings.repo_url) {
      results['Repository Access'].push(await this.checkRepositoryAccess(settings.repo_url));
    }
    if (settings.repo_path) {
      results['Repository Access'].push(await this.checkLocalRepository(settings.repo_path));
    }
    results['Repository Access'].push(await this.checkBranchExists(settings));

    // File System
    results['File System'].push(await this.checkDirectoryPermissions(settings.stage_dir));
    results['File System'].push(await this.checkDirectoryPermissions('./results'));

    return results;
  }

  async checkNodeVersion() {
    try {
      const version = process.version;
      const majorVersion = parseInt(version.slice(1).split('.')[0]);
      return {
        name: 'Node.js version',
        valid: majorVersion >= 22,
        message: majorVersion >= 22 ? `${version} (✓)` : `${version} - requires >= 22.0.0`
      };
    } catch (error) {
      return {
        name: 'Node.js version',
        valid: false,
        message: 'Unable to determine Node.js version'
      };
    }
  }

  async checkGitVersion() {
    try {
      const version = execSync('git --version', { encoding: 'utf8' }).trim();
      const versionMatch = version.match(/git version (\d+)\.(\d+)\.(\d+)/);
      
      if (!versionMatch) {
        return {
          name: 'Git version',
          valid: false,
          message: 'Unable to parse Git version'
        };
      }

      const [, major, minor] = versionMatch;
      const isValid = parseInt(major) > 2 || (parseInt(major) === 2 && parseInt(minor) >= 30);
      
      return {
        name: 'Git version',
        valid: isValid,
        message: isValid ? `${version} (✓)` : `${version} - requires >= 2.30.0`
      };
    } catch (error) {
      return {
        name: 'Git version',
        valid: false,
        message: 'Git not found or not accessible'
      };
    }
  }

  async checkDiskSpace() {
    try {
      const stats = await fs.stat(process.cwd());
      // This is a simplified check - in production, you'd want to check actual available space
      return {
        name: 'Disk space',
        valid: true,
        message: 'Directory accessible (detailed space check not implemented)'
      };
    } catch (error) {
      return {
        name: 'Disk space',
        valid: false,
        message: 'Unable to access current directory'
      };
    }
  }

  async checkGitConfig() {
    try {
      const userName = execSync('git config user.name', { encoding: 'utf8' }).trim();
      const userEmail = execSync('git config user.email', { encoding: 'utf8' }).trim();
      
      return {
        name: 'Git configuration',
        valid: userName && userEmail,
        message: userName && userEmail ? 'Git user configured' : 'Git user.name and user.email not configured'
      };
    } catch (error) {
      return {
        name: 'Git configuration',
        valid: false,
        message: 'Git configuration check failed'
      };
    }
  }

  async checkAgentTool(agentName) {
    const commands = {
      'Augment CLI': 'auggie',
      'Claude Code': 'claude',
      'Cursor CLI': 'cursor-agent'
    };

    const command = commands[agentName];
    if (!command) {
      return {
        name: agentName,
        valid: false,
        message: 'Unknown agent'
      };
    }

    try {
      execSync(`which ${command}`, { encoding: 'utf8', stdio: 'pipe' });
      return {
        name: agentName,
        valid: true,
        message: `${command} found`
      };
    } catch (error) {
      return {
        name: agentName,
        valid: false,
        message: `${command} not found in PATH`
      };
    }
  }

  async checkLLMConfiguration() {
    const provider = process.env.LLM_PROVIDER;
    const apiKey = process.env.LLM_API_KEY;

    if (!provider || !apiKey) {
      return {
        name: 'LLM configuration',
        valid: false,
        message: 'LLM_PROVIDER and LLM_API_KEY environment variables required'
      };
    }

    // Basic connectivity check (simplified)
    return {
      name: 'LLM configuration',
      valid: true,
      message: `Provider: ${provider} (API key configured)`
    };
  }

  async checkRepositoryAccess(repoUrl) {
    try {
      // Simple URL validation
      new URL(repoUrl);
      return {
        name: 'Repository URL',
        valid: true,
        message: 'URL format valid'
      };
    } catch (error) {
      return {
        name: 'Repository URL',
        valid: false,
        message: 'Invalid URL format'
      };
    }
  }

  async checkLocalRepository(repoPath) {
    try {
      const fullPath = path.resolve(repoPath);
      const exists = await fs.pathExists(fullPath);
      
      if (!exists) {
        return {
          name: 'Local repository',
          valid: false,
          message: `Path does not exist: ${fullPath}`
        };
      }

      const gitDir = path.join(fullPath, '.git');
      const isGitRepo = await fs.pathExists(gitDir);
      
      return {
        name: 'Local repository',
        valid: isGitRepo,
        message: isGitRepo ? 'Valid Git repository' : 'Not a Git repository'
      };
    } catch (error) {
      return {
        name: 'Local repository',
        valid: false,
        message: `Access error: ${error.message}`
      };
    }
  }

  async checkBranchExists(settings) {
    // This is a simplified check - full implementation would verify branch exists
    return {
      name: 'Branch configuration',
      valid: true,
      message: `Target branch: ${settings.branch}`
    };
  }

  async checkDirectoryPermissions(dirPath) {
    try {
      const fullPath = path.resolve(dirPath);
      await fs.ensureDir(fullPath);
      
      // Test write permissions
      const testFile = path.join(fullPath, '.write-test');
      await fs.writeFile(testFile, 'test');
      await fs.remove(testFile);
      
      return {
        name: `Directory: ${dirPath}`,
        valid: true,
        message: 'Read/write permissions verified'
      };
    } catch (error) {
      return {
        name: `Directory: ${dirPath}`,
        valid: false,
        message: `Permission error: ${error.message}`
      };
    }
  }
}

module.exports = { SettingsManager };
