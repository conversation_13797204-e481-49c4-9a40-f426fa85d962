const { logger } = require('../utils/Logger');
const { LLMEvaluatorMode } = require('../modes/LLMEvaluatorMode');
const { PRRecreateMode } = require('../modes/PRRecreateMode');

/**
 * Benchmark Runner - Orchestrates benchmark execution
 * Coordinates mode selection, execution, and result handling
 */
class BenchmarkRunner {
  constructor(settings) {
    this.settings = settings;
    this.logger = logger.child({ component: 'BenchmarkRunner' });
    this.mode = null;
    this.startTime = null;
    this.endTime = null;
  }

  /**
   * Execute benchmark based on settings
   * @returns {Promise<Object>} Execution results
   */
  async execute() {
    try {
      this.logger.info('Starting benchmark execution', {
        mode: this.settings.mode,
        agents: this.settings.agents,
        metrics: this.settings.metrics
      });

      this.startTime = Date.now();

      // Validate settings
      await this.validateExecution();

      // Initialize mode
      this.mode = this.createMode();

      // Execute benchmark
      const results = await this.executeBenchmark();

      this.endTime = Date.now();
      const totalTime = this.endTime - this.startTime;

      this.logger.info('Benchmark execution completed successfully', {
        totalTime: `${totalTime}ms`,
        mode: this.settings.mode
      });

      return {
        ...results,
        executionTime: totalTime,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.endTime = Date.now();
      this.logger.error('Benchmark execution failed', {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Validate execution prerequisites
   * @returns {Promise<void>}
   */
  async validateExecution() {
    try {
      this.logger.debug('Validating execution prerequisites');

      // Check mode is supported
      if (!['LLM_Evaluator', 'PR_Recreate'].includes(this.settings.mode)) {
        throw new Error(`Unsupported mode: ${this.settings.mode}`);
      }

      // Check agents are available
      if (!this.settings.agents || this.settings.agents.length === 0) {
        throw new Error('No agents specified for execution');
      }

      // Check metrics are specified
      if (!this.settings.metrics || this.settings.metrics.length === 0) {
        throw new Error('No metrics specified for execution');
      }

      // Mode-specific validation
      if (this.settings.mode === 'LLM_Evaluator') {
        await this.validateLLMEvaluatorMode();
      } else if (this.settings.mode === 'PR_Recreate') {
        await this.validatePRRecreateMode();
      }

      this.logger.debug('Execution prerequisites validated successfully');
    } catch (error) {
      this.logger.error('Execution validation failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Validate LLM Evaluator mode prerequisites
   * @returns {Promise<void>}
   */
  async validateLLMEvaluatorMode() {
    if (!this.settings.repo_url && !this.settings.repo_path) {
      throw new Error('LLM_Evaluator mode requires repo_url or repo_path');
    }

    if (!this.settings.LLM_Evaluator) {
      throw new Error('LLM_Evaluator configuration is required');
    }

    if (!this.settings.LLM_Evaluator.num_prompts || this.settings.LLM_Evaluator.num_prompts <= 0) {
      throw new Error('LLM_Evaluator.num_prompts must be specified and greater than 0');
    }
  }

  /**
   * Validate PR Recreate mode prerequisites
   * @returns {Promise<void>}
   */
  async validatePRRecreateMode() {
    if (!this.settings.repo_url) {
      throw new Error('PR_Recreate mode requires repo_url');
    }

    if (!this.settings.PR_Recreate) {
      throw new Error('PR_Recreate configuration is required');
    }

    if (!this.settings.PR_Recreate.num_prs || this.settings.PR_Recreate.num_prs <= 0) {
      throw new Error('PR_Recreate.num_prs must be specified and greater than 0');
    }
  }

  /**
   * Create mode instance based on settings
   * @returns {Object} Mode instance
   */
  createMode() {
    try {
      this.logger.debug('Creating mode instance', { mode: this.settings.mode });

      let modeInstance;

      switch (this.settings.mode) {
        case 'LLM_Evaluator':
          modeInstance = new LLMEvaluatorMode(this.settings);
          break;
        case 'PR_Recreate':
          modeInstance = new PRRecreateMode(this.settings);
          break;
        default:
          throw new Error(`Unsupported mode: ${this.settings.mode}`);
      }

      this.logger.debug('Mode instance created successfully');
      return modeInstance;
    } catch (error) {
      this.logger.error('Failed to create mode instance', { error: error.message });
      throw error;
    }
  }

  /**
   * Execute benchmark using the selected mode
   * @returns {Promise<Object>} Benchmark results
   */
  async executeBenchmark() {
    try {
      this.logger.info('Executing benchmark', { mode: this.settings.mode });

      // Execute the mode
      const results = await this.mode.execute();

      // Add execution metadata
      const enhancedResults = {
        ...results,
        mode: this.settings.mode,
        settings: this.settings,
        executionMetadata: {
          startTime: this.startTime,
          endTime: this.endTime || Date.now(),
          version: '1.0.0'
        }
      };

      return enhancedResults;
    } catch (error) {
      this.logger.error('Benchmark execution failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Get execution progress
   * @returns {Object} Progress information
   */
  getProgress() {
    const baseProgress = {
      mode: this.settings.mode,
      startTime: this.startTime,
      currentTime: Date.now(),
      elapsedTime: this.startTime ? Date.now() - this.startTime : 0
    };

    if (this.mode && typeof this.mode.getProgress === 'function') {
      return {
        ...baseProgress,
        ...this.mode.getProgress()
      };
    }

    return baseProgress;
  }

  /**
   * Stop execution gracefully
   * @returns {Promise<void>}
   */
  async stop() {
    try {
      this.logger.info('Stopping benchmark execution');

      if (this.mode && typeof this.mode.stop === 'function') {
        await this.mode.stop();
      }

      this.endTime = Date.now();
      this.logger.info('Benchmark execution stopped');
    } catch (error) {
      this.logger.error('Failed to stop benchmark execution', { error: error.message });
      throw error;
    }
  }

  /**
   * Clean up resources
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      this.logger.info('Cleaning up benchmark resources');

      if (this.mode && typeof this.mode.cleanup === 'function') {
        await this.mode.cleanup();
      }

      this.logger.info('Benchmark cleanup completed');
    } catch (error) {
      this.logger.warn('Failed to cleanup benchmark resources', { error: error.message });
    }
  }
}

module.exports = { BenchmarkRunner };
