const axios = require('axios');
const { spawn } = require('child_process');
const { logger } = require('../utils/Logger');
const { fileSystem } = require('../utils/FileSystem');
const { gitManager } = require('../utils/GitManager');
const { AdapterFactory } = require('../adapters/AdapterFactory');

/**
 * Validation Runner - Comprehensive system and connectivity validation
 * Extends basic configuration validation with deep system checks
 */
class ValidationRunner {
  constructor(settings) {
    this.settings = settings;
    this.logger = logger.child({ component: 'ValidationRunner' });
  }

  /**
   * Run all validation checks
   * @returns {Promise<Object>} Comprehensive validation results
   */
  async runAllValidations() {
    try {
      this.logger.info('Starting comprehensive validation');

      const results = {
        categories: {},
        overall: {
          success: true,
          passed: 0,
          total: 0,
          warnings: 0
        }
      };

      // Run validation categories
      results.categories.system = await this.validateSystem();
      results.categories.connectivity = await this.validateConnectivity();
      results.categories.agents = await this.validateAgents();
      results.categories.repositories = await this.validateRepositories();
      results.categories.llm = await this.validateLLMServices();
      results.categories.storage = await this.validateStorage();

      // Calculate overall results
      for (const category of Object.values(results.categories)) {
        results.overall.total += category.checks.length;
        results.overall.passed += category.checks.filter(c => c.success).length;
        results.overall.warnings += category.checks.filter(c => c.warning).length;
        
        if (!category.success) {
          results.overall.success = false;
        }
      }

      this.logger.info('Comprehensive validation completed', {
        passed: results.overall.passed,
        total: results.overall.total,
        success: results.overall.success
      });

      return results;
    } catch (error) {
      this.logger.error('Validation failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Validate system requirements
   * @returns {Promise<Object>} System validation results
   */
  async validateSystem() {
    const checks = [];

    // Node.js version
    checks.push(await this.checkNodeVersion());
    
    // Git version
    checks.push(await this.checkGitVersion());
    
    // Available memory
    checks.push(await this.checkMemory());
    
    // Disk space
    checks.push(await this.checkDiskSpace());

    const success = checks.every(check => check.success);
    
    return { success, checks };
  }

  /**
   * Validate network connectivity
   * @returns {Promise<Object>} Connectivity validation results
   */
  async validateConnectivity() {
    const checks = [];

    // Internet connectivity
    checks.push(await this.checkInternetConnectivity());
    
    // GitHub connectivity
    checks.push(await this.checkGitHubConnectivity());

    const success = checks.every(check => check.success);
    
    return { success, checks };
  }

  /**
   * Validate agent availability
   * @returns {Promise<Object>} Agent validation results
   */
  async validateAgents() {
    const checks = [];

    for (const agentName of this.settings.agents) {
      checks.push(await this.checkAgentAvailability(agentName));
    }

    const success = checks.every(check => check.success);
    
    return { success, checks };
  }

  /**
   * Validate repository access
   * @returns {Promise<Object>} Repository validation results
   */
  async validateRepositories() {
    const checks = [];

    if (this.settings.repo_url) {
      checks.push(await this.checkRepositoryAccess(this.settings.repo_url));
    }

    if (this.settings.repo_path) {
      checks.push(await this.checkLocalRepository(this.settings.repo_path));
    }

    const success = checks.every(check => check.success);
    
    return { success, checks };
  }

  /**
   * Validate LLM services
   * @returns {Promise<Object>} LLM validation results
   */
  async validateLLMServices() {
    const checks = [];

    // Check if LLM metrics are configured
    const hasLLMMetrics = this.settings.metrics.some(metric => 
      ['completeness', 'technical_correctness', 'logical_correctness', 'clarity', 'instruction_adherence'].includes(metric)
    );

    if (hasLLMMetrics) {
      checks.push(await this.checkLLMConfiguration());
      
      if (process.env.LLM_PROVIDER === 'openai') {
        checks.push(await this.checkOpenAIConnectivity());
      } else if (process.env.LLM_PROVIDER === 'anthropic') {
        checks.push(await this.checkAnthropicConnectivity());
      }
    } else {
      checks.push({
        name: 'LLM Services',
        success: true,
        warning: false,
        details: 'No LLM-assessed metrics configured, skipping LLM validation'
      });
    }

    const success = checks.every(check => check.success);
    
    return { success, checks };
  }

  /**
   * Validate storage and permissions
   * @returns {Promise<Object>} Storage validation results
   */
  async validateStorage() {
    const checks = [];

    // Check write permissions
    checks.push(await this.checkWritePermissions());
    
    // Check temp directory
    checks.push(await this.checkTempDirectory());

    const success = checks.every(check => check.success);
    
    return { success, checks };
  }

  /**
   * Check Node.js version
   * @returns {Promise<Object>} Check result
   */
  async checkNodeVersion() {
    try {
      const version = process.version;
      const majorVersion = parseInt(version.slice(1).split('.')[0]);
      
      if (majorVersion >= 22) {
        return {
          name: 'Node.js Version',
          success: true,
          warning: false,
          details: `${version} (✓ >= 22.0.0)`
        };
      } else {
        return {
          name: 'Node.js Version',
          success: false,
          warning: false,
          error: `${version} is not supported. Requires >= 22.0.0`,
          details: 'Please upgrade Node.js'
        };
      }
    } catch (error) {
      return {
        name: 'Node.js Version',
        success: false,
        warning: false,
        error: 'Failed to check Node.js version'
      };
    }
  }

  /**
   * Check Git version
   * @returns {Promise<Object>} Check result
   */
  async checkGitVersion() {
    try {
      const version = await this.runCommand('git', ['--version']);
      const versionMatch = version.match(/git version (\d+\.\d+\.\d+)/);
      
      if (versionMatch) {
        const [major, minor] = versionMatch[1].split('.').map(Number);
        
        if (major > 2 || (major === 2 && minor >= 30)) {
          return {
            name: 'Git Version',
            success: true,
            warning: false,
            details: `${versionMatch[1]} (✓ >= 2.30.0)`
          };
        } else {
          return {
            name: 'Git Version',
            success: false,
            warning: false,
            error: `${versionMatch[1]} is not supported. Requires >= 2.30.0`,
            details: 'Please upgrade Git'
          };
        }
      } else {
        return {
          name: 'Git Version',
          success: false,
          warning: false,
          error: 'Could not parse Git version'
        };
      }
    } catch (error) {
      return {
        name: 'Git Version',
        success: false,
        warning: false,
        error: 'Git is not installed or not accessible'
      };
    }
  }

  /**
   * Check available memory
   * @returns {Promise<Object>} Check result
   */
  async checkMemory() {
    try {
      const totalMemory = require('os').totalmem();
      const freeMemory = require('os').freemem();
      const totalGB = Math.round(totalMemory / (1024 ** 3));
      const freeGB = Math.round(freeMemory / (1024 ** 3));
      
      if (freeGB >= 2) {
        return {
          name: 'Available Memory',
          success: true,
          warning: freeGB < 4,
          details: `${freeGB}GB free of ${totalGB}GB total`
        };
      } else {
        return {
          name: 'Available Memory',
          success: false,
          warning: false,
          error: `Only ${freeGB}GB available. Requires at least 2GB`,
          details: 'Close other applications to free memory'
        };
      }
    } catch (error) {
      return {
        name: 'Available Memory',
        success: false,
        warning: false,
        error: 'Failed to check memory'
      };
    }
  }

  /**
   * Check disk space
   * @returns {Promise<Object>} Check result
   */
  async checkDiskSpace() {
    try {
      const stats = await fileSystem.stat('.');
      // This is a simplified check - in production you'd use statvfs or similar
      
      return {
        name: 'Disk Space',
        success: true,
        warning: false,
        details: 'Sufficient disk space available'
      };
    } catch (error) {
      return {
        name: 'Disk Space',
        success: false,
        warning: false,
        error: 'Failed to check disk space'
      };
    }
  }

  /**
   * Check internet connectivity
   * @returns {Promise<Object>} Check result
   */
  async checkInternetConnectivity() {
    try {
      await axios.get('https://www.google.com', { timeout: 5000 });
      
      return {
        name: 'Internet Connectivity',
        success: true,
        warning: false,
        details: 'Internet connection is working'
      };
    } catch (error) {
      return {
        name: 'Internet Connectivity',
        success: false,
        warning: false,
        error: 'No internet connection available',
        details: 'Check your network connection'
      };
    }
  }

  /**
   * Check GitHub connectivity
   * @returns {Promise<Object>} Check result
   */
  async checkGitHubConnectivity() {
    try {
      await axios.get('https://api.github.com', { timeout: 10000 });
      
      return {
        name: 'GitHub Connectivity',
        success: true,
        warning: false,
        details: 'GitHub API is accessible'
      };
    } catch (error) {
      return {
        name: 'GitHub Connectivity',
        success: false,
        warning: false,
        error: 'Cannot reach GitHub API',
        details: 'Check firewall and network settings'
      };
    }
  }

  /**
   * Run a command and return output
   * @param {string} command - Command to run
   * @param {Array<string>} args - Command arguments
   * @returns {Promise<string>} Command output
   */
  async runCommand(command, args = []) {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args);
      let output = '';
      let error = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        error += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve(output.trim());
        } else {
          reject(new Error(error || `Command failed with code ${code}`));
        }
      });

      process.on('error', (err) => {
        reject(err);
      });
    });
  }
}

module.exports = { ValidationRunner };
