const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const { SettingsManager } = require('../config/SettingsManager');
const { BenchmarkRunner } = require('./BenchmarkRunner');

/**
 * Main CLI interface for Augbench
 * Handles command parsing and routing
 */
class BenchmarkCLI {
  constructor() {
    this.program = new Command();
    this.setupCommands();
  }

  setupCommands() {
    this.program
      .name('augbench')
      .description('AI Coding Assistant Benchmarking Tool')
      .version('1.0.0');

    // Benchmark command
    this.program
      .command('benchmark')
      .description('Execute benchmarking based on settings.json configuration')
      .action(async () => {
        await this.runBenchmark();
      });

    // Validate command
    this.program
      .command('validate')
      .description('Check prerequisites and validate configuration')
      .action(async () => {
        await this.runValidation();
      });

    // Help command
    this.program
      .command('help')
      .description('Show comprehensive examples and usage information')
      .action(() => {
        this.showHelp();
      });
  }

  async run(argv) {
    try {
      await this.program.parseAsync(argv);
    } catch (error) {
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  }

  async runBenchmark() {
    console.log(chalk.blue('🚀 Starting Augbench Benchmark...'));
    
    try {
      // Load and validate settings
      const settingsManager = new SettingsManager();
      const settings = await settingsManager.loadSettings();
      
      console.log(chalk.green('✓ Configuration loaded successfully'));
      console.log(chalk.gray(`Mode: ${settings.mode}`));
      console.log(chalk.gray(`Agents: ${settings.agents.join(', ')}`));
      
      // Initialize and run benchmark
      const runner = new BenchmarkRunner(settings);
      await runner.execute();
      
      console.log(chalk.green('🎉 Benchmark completed successfully!'));
      
    } catch (error) {
      console.error(chalk.red('❌ Benchmark failed:'), error.message);
      if (process.env.DEBUG) {
        console.error(chalk.gray(error.stack));
      }
      process.exit(1);
    }
  }

  async runValidation() {
    console.log(chalk.blue('🔍 Validating Augbench Prerequisites...'));
    
    try {
      const settingsManager = new SettingsManager();
      const validationResults = await settingsManager.validateEnvironment();
      
      // Display validation results
      console.log('\n' + chalk.bold('Validation Results:'));
      console.log('='.repeat(50));
      
      let allValid = true;
      
      for (const [category, checks] of Object.entries(validationResults)) {
        console.log(`\n${chalk.bold(category)}:`);
        
        for (const check of checks) {
          const icon = check.valid ? chalk.green('✓') : chalk.red('✗');
          const status = check.valid ? chalk.green('PASS') : chalk.red('FAIL');
          console.log(`  ${icon} ${check.name}: ${status}`);
          
          if (!check.valid) {
            console.log(`    ${chalk.yellow('→')} ${check.message}`);
            allValid = false;
          }
        }
      }
      
      console.log('\n' + '='.repeat(50));
      
      if (allValid) {
        console.log(chalk.green('🎉 All prerequisites validated successfully!'));
        console.log(chalk.gray('You can now run: augbench benchmark'));
      } else {
        console.log(chalk.red('❌ Some prerequisites failed validation.'));
        console.log(chalk.yellow('Please address the issues above before running benchmarks.'));
        process.exit(1);
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Validation failed:'), error.message);
      if (process.env.DEBUG) {
        console.error(chalk.gray(error.stack));
      }
      process.exit(1);
    }
  }

  showHelp() {
    console.log(chalk.bold.blue('\n🔧 Augbench - AI Coding Assistant Benchmarking Tool\n'));
    
    console.log(chalk.bold('COMMANDS:'));
    console.log('  augbench benchmark  Execute benchmarking based on settings.json');
    console.log('  augbench validate   Check prerequisites and configuration');
    console.log('  augbench help       Show this help information\n');
    
    console.log(chalk.bold('SETUP:'));
    console.log('1. Copy configuration template:');
    console.log(chalk.gray('   cp settings.json.llm.example settings.json    # For LLM_Evaluator mode'));
    console.log(chalk.gray('   cp settings.json.pr.example settings.json     # For PR_Recreate mode\n'));
    
    console.log('2. Set up environment variables:');
    console.log(chalk.gray('   cp .env.example .env'));
    console.log(chalk.gray('   # Edit .env with your API keys\n'));
    
    console.log(chalk.bold('LLM_EVALUATOR MODE EXAMPLE:'));
    console.log(chalk.gray('Tests agents on custom prompts and coding tasks'));
    console.log(chalk.yellow('Configuration (settings.json):'));
    console.log(chalk.gray(`{
  "mode": "LLM_Evaluator",
  "agents": ["Augment CLI", "Claude Code"],
  "repo_url": "https://github.com/example/repo.git",
  "runs_per_prompt": 3,
  "metrics": ["response_time", "completeness", "clarity"],
  "LLM_Evaluator": {
    "generate_prompts": true,
    "prompt_topics": [
      "Add unit tests for the main module",
      "Create API documentation"
    ]
  }
}`));
    
    console.log('\n' + chalk.bold('PR_RECREATE MODE EXAMPLE:'));
    console.log(chalk.gray('Benchmarks agents by recreating actual Pull Requests'));
    console.log(chalk.yellow('Configuration (settings.json):'));
    console.log(chalk.gray(`{
  "mode": "PR_Recreate",
  "agents": ["Augment CLI", "Claude Code"],
  "repo_url": "https://github.com/example/repo.git",
  "metrics": ["response_time", "ast_similarity", "completeness"],
  "PR_Recreate": {
    "num_prs": 5
  }
}`));
    
    console.log('\n' + chalk.bold('SUPPORTED AGENTS:'));
    console.log('• Augment CLI (auggie) - https://docs.augmentcode.com/cli/overview');
    console.log('• Claude Code (claude) - https://docs.anthropic.com/en/docs/claude-code/overview');
    console.log('• Cursor CLI (cursor-agent) - https://docs.cursor.com/en/cli/overview\n');
    
    console.log(chalk.bold('REQUIREMENTS:'));
    console.log('• Node.js >= 22.0.0');
    console.log('• Git >= 2.30.0');
    console.log('• At least one supported AI assistant CLI tool');
    console.log('• 10GB+ available disk space');
    console.log('• API keys for LLM evaluation (OpenAI/Anthropic)\n');
    
    console.log(chalk.bold('OUTPUT:'));
    console.log('• JSON results in ./results/<output_filename>.json');
    console.log('• PNG charts in ./results/<output_filename>_<metric>.png');
    console.log('• Detailed logs in ./stage/ directory\n');
  }
}

module.exports = { BenchmarkCLI };
