const path = require('path');
const { logger } = require('../utils/Logger');
const { fileSystem } = require('../utils/FileSystem');
const { gitManager } = require('../utils/GitManager');
const { promptGenerator } = require('../utils/PromptGenerator');
const { resultsStorage } = require('../utils/ResultsStorage');
const { AdapterFactory } = require('../adapters/AdapterFactory');
const { MetricsFactory } = require('../metrics/MetricsFactory');

/**
 * LLM Evaluator Mode - Evaluates AI assistants using generated prompts
 * Workflow: clone repos to stage/<agent>/, check/generate prompts, execute agents, measure metrics
 */
class LLMEvaluatorMode {
  constructor(settings, options = {}) {
    this.settings = settings;
    this.options = {
      logger: logger,
      stageDir: './stage',
      promptsDir: './prompts',
      ...options
    };

    this.logger = this.options.logger.child({ mode: 'LLMEvaluator' });
    this.stageDir = this.options.stageDir;
    this.promptsDir = this.options.promptsDir;
    
    this.validateSettings();
    this.initializeComponents();
  }

  /**
   * Validate settings for LLM Evaluator mode
   */
  validateSettings() {
    if (this.settings.mode !== 'LLM_Evaluator') {
      throw new Error('Settings mode must be LLM_Evaluator');
    }

    if (!this.settings.repo_url && !this.settings.repo_path) {
      throw new Error('Either repo_url or repo_path must be specified');
    }

    if (!this.settings.agents || this.settings.agents.length === 0) {
      throw new Error('At least one agent must be specified');
    }

    if (!this.settings.metrics || this.settings.metrics.length === 0) {
      throw new Error('At least one metric must be specified');
    }

    this.logger.debug('Settings validated successfully');
  }

  /**
   * Initialize components
   */
  initializeComponents() {
    try {
      // Create adapters for each agent
      this.adapters = {};
      for (const agentName of this.settings.agents) {
        this.adapters[agentName] = AdapterFactory.createAdapter(agentName);
      }

      // Create metrics instances
      this.metrics = MetricsFactory.createMetricsFromSettings(this.settings);

      this.logger.info('Components initialized', {
        agents: this.settings.agents.length,
        metrics: this.metrics.length
      });
    } catch (error) {
      this.logger.error('Failed to initialize components', { error: error.message });
      throw error;
    }
  }

  /**
   * Execute LLM Evaluator mode workflow
   * @returns {Promise<Object>} Execution results
   */
  async execute() {
    try {
      this.logger.info('Starting LLM Evaluator mode execution');

      // Initialize stage directory
      await this.initializeStage();

      // Clone or prepare repositories for each agent
      await this.prepareRepositories();

      // Check for existing prompts or generate new ones
      const prompts = await this.preparePrompts();

      // Execute agents on prompts
      const executionResults = await this.executeAgents(prompts);

      // Measure metrics
      const metricResults = await this.measureMetrics(executionResults);

      // Save results
      const results = await this.saveResults(metricResults);

      this.logger.info('LLM Evaluator mode execution completed successfully', {
        prompts: prompts.length,
        totalRuns: executionResults.length,
        agents: this.settings.agents.length
      });

      return results;
    } catch (error) {
      this.logger.error('LLM Evaluator mode execution failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Initialize stage directory structure
   * @returns {Promise<void>}
   */
  async initializeStage() {
    try {
      this.logger.debug('Initializing stage directory');

      // Create main stage directory
      await fileSystem.ensureDir(this.stageDir);

      // Create agent-specific directories
      for (const agentName of this.settings.agents) {
        const agentDir = path.join(this.stageDir, agentName);
        await fileSystem.ensureDir(agentDir);
      }

      // Create prompts directory
      await fileSystem.ensureDir(this.promptsDir);

      this.logger.debug('Stage directory initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize stage directory', { error: error.message });
      throw error;
    }
  }

  /**
   * Prepare repositories for each agent
   * @returns {Promise<void>}
   */
  async prepareRepositories() {
    try {
      this.logger.info('Preparing repositories for agents');

      const repoOperations = this.settings.agents.map(async (agentName) => {
        const agentDir = path.join(this.stageDir, agentName);
        
        if (this.settings.repo_url) {
          await this.cloneRepository(this.settings.repo_url, agentDir, agentName);
        } else if (this.settings.repo_path) {
          await this.copyRepository(this.settings.repo_path, agentDir, agentName);
        }
      });

      if (this.settings.parallel_agents) {
        await Promise.all(repoOperations);
      } else {
        for (const operation of repoOperations) {
          await operation;
        }
      }

      this.logger.info('Repositories prepared successfully');
    } catch (error) {
      this.logger.error('Failed to prepare repositories', { error: error.message });
      throw error;
    }
  }

  /**
   * Clone repository for an agent
   * @param {string} repoUrl - Repository URL
   * @param {string} targetDir - Target directory
   * @param {string} agentName - Agent name
   * @returns {Promise<void>}
   */
  async cloneRepository(repoUrl, targetDir, agentName) {
    try {
      this.logger.debug('Cloning repository for agent', { agentName, repoUrl });

      // Remove existing directory if it exists
      if (await fileSystem.exists(targetDir)) {
        await fileSystem.remove(targetDir);
      }

      // Clone repository
      await gitManager.clone(repoUrl, targetDir);

      // Checkout specific branch if specified
      if (this.settings.branch && this.settings.branch !== 'main') {
        await gitManager.checkout(targetDir, this.settings.branch);
      }

      this.logger.debug('Repository cloned successfully', { agentName });
    } catch (error) {
      this.logger.error('Failed to clone repository', { 
        agentName, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Copy local repository for an agent
   * @param {string} repoPath - Source repository path
   * @param {string} targetDir - Target directory
   * @param {string} agentName - Agent name
   * @returns {Promise<void>}
   */
  async copyRepository(repoPath, targetDir, agentName) {
    try {
      this.logger.debug('Copying repository for agent', { agentName, repoPath });

      // Remove existing directory if it exists
      if (await fileSystem.exists(targetDir)) {
        await fileSystem.remove(targetDir);
      }

      // Copy repository
      await fileSystem.copy(repoPath, targetDir);

      this.logger.debug('Repository copied successfully', { agentName });
    } catch (error) {
      this.logger.error('Failed to copy repository', { 
        agentName, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Prepare prompts for execution
   * @returns {Promise<Array<Object>>} Prompts array
   */
  async preparePrompts() {
    try {
      this.logger.info('Preparing prompts');

      let prompts = [];

      // Check for existing prompts
      const existingPrompts = await this.checkExistingPrompts();
      
      if (existingPrompts.length > 0) {
        this.logger.info('Using existing prompts', { count: existingPrompts.length });
        prompts = existingPrompts;
      } else {
        // Generate new prompts
        this.logger.info('Generating new prompts');
        prompts = await this.generatePrompts();
        
        // Save generated prompts
        await this.savePrompts(prompts);
      }

      this.logger.info('Prompts prepared successfully', { count: prompts.length });
      return prompts;
    } catch (error) {
      this.logger.error('Failed to prepare prompts', { error: error.message });
      throw error;
    }
  }

  /**
   * Check for existing prompts
   * @returns {Promise<Array<Object>>} Existing prompts
   */
  async checkExistingPrompts() {
    try {
      const promptsFile = path.join(this.promptsDir, 'generated_prompts.json');
      
      if (await fileSystem.exists(promptsFile)) {
        const data = await fileSystem.readJson(promptsFile);
        return data.prompts || [];
      }

      return [];
    } catch (error) {
      this.logger.warn('Failed to check existing prompts', { error: error.message });
      return [];
    }
  }

  /**
   * Generate new prompts
   * @returns {Promise<Array<Object>>} Generated prompts
   */
  async generatePrompts() {
    try {
      const repoPath = this.settings.repo_path || path.join(this.stageDir, this.settings.agents[0]);
      
      const prompts = await promptGenerator.generatePromptsFromRepository(repoPath, {
        numPrompts: this.settings.LLM_Evaluator.num_prompts,
        topics: this.settings.LLM_Evaluator.topics || [],
        complexity: this.settings.LLM_Evaluator.complexity || 'medium',
        includeContext: true
      });

      return prompts;
    } catch (error) {
      this.logger.error('Failed to generate prompts', { error: error.message });
      throw error;
    }
  }

  /**
   * Save prompts to file
   * @param {Array<Object>} prompts - Prompts to save
   * @returns {Promise<void>}
   */
  async savePrompts(prompts) {
    try {
      const promptsFile = path.join(this.promptsDir, 'generated_prompts.json');
      
      await fileSystem.writeJson(promptsFile, {
        prompts,
        metadata: {
          generated: new Date().toISOString(),
          mode: 'LLM_Evaluator',
          settings: this.settings
        }
      });

      this.logger.debug('Prompts saved successfully', { file: promptsFile });
    } catch (error) {
      this.logger.error('Failed to save prompts', { error: error.message });
      throw error;
    }
  }

  /**
   * Execute agents on prompts
   * @param {Array<Object>} prompts - Prompts to execute
   * @returns {Promise<Array<Object>>} Execution results
   */
  async executeAgents(prompts) {
    try {
      this.logger.info('Executing agents on prompts', {
        prompts: prompts.length,
        agents: this.settings.agents.length,
        runsPerPrompt: this.settings.runs_per_prompt
      });

      const executionResults = [];

      for (const prompt of prompts) {
        const promptResults = await this.executePrompt(prompt);
        executionResults.push(...promptResults);
      }

      this.logger.info('Agent execution completed', {
        totalRuns: executionResults.length
      });

      return executionResults;
    } catch (error) {
      this.logger.error('Failed to execute agents', { error: error.message });
      throw error;
    }
  }

  /**
   * Execute a single prompt across all agents
   * @param {Object} prompt - Prompt to execute
   * @returns {Promise<Array<Object>>} Prompt execution results
   */
  async executePrompt(prompt) {
    try {
      this.logger.debug('Executing prompt', { promptId: prompt.id });

      const promptResults = [];

      const agentOperations = this.settings.agents.map(async (agentName) => {
        const agentResults = await this.executeAgentRuns(prompt, agentName);
        return agentResults;
      });

      let allAgentResults;
      if (this.settings.parallel_agents) {
        allAgentResults = await Promise.all(agentOperations);
      } else {
        allAgentResults = [];
        for (const operation of agentOperations) {
          const result = await operation;
          allAgentResults.push(result);
        }
      }

      // Flatten results
      for (const agentResults of allAgentResults) {
        promptResults.push(...agentResults);
      }

      return promptResults;
    } catch (error) {
      this.logger.error('Failed to execute prompt', { 
        promptId: prompt.id, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Execute multiple runs for an agent on a prompt
   * @param {Object} prompt - Prompt to execute
   * @param {string} agentName - Agent name
   * @returns {Promise<Array<Object>>} Agent run results
   */
  async executeAgentRuns(prompt, agentName) {
    try {
      const adapter = this.adapters[agentName];
      const agentDir = path.join(this.stageDir, agentName);
      const runResults = [];

      for (let runId = 1; runId <= this.settings.runs_per_prompt; runId++) {
        try {
          this.logger.debug('Executing agent run', { 
            agentName, 
            promptId: prompt.id, 
            runId 
          });

          const startTime = Date.now();
          
          // Execute agent
          const result = await adapter.execute(prompt.text, {
            workingDirectory: agentDir,
            promptId: prompt.id,
            runId: runId,
            timeout: this.settings.execution?.timeout || 300000
          });

          const endTime = Date.now();
          const executionTime = endTime - startTime;

          runResults.push({
            prompt: prompt.id,
            agent: agentName,
            runId: runId,
            executionTime: executionTime,
            output: result.output,
            metadata: result.metadata,
            success: true,
            timestamp: new Date().toISOString()
          });

          this.logger.debug('Agent run completed successfully', { 
            agentName, 
            promptId: prompt.id, 
            runId,
            executionTime: `${executionTime}ms`
          });

        } catch (error) {
          this.logger.warn('Agent run failed', { 
            agentName, 
            promptId: prompt.id, 
            runId, 
            error: error.message 
          });

          runResults.push({
            prompt: prompt.id,
            agent: agentName,
            runId: runId,
            executionTime: null,
            output: null,
            metadata: null,
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
          });
        }
      }

      return runResults;
    } catch (error) {
      this.logger.error('Failed to execute agent runs', { 
        agentName, 
        promptId: prompt.id, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Measure metrics for execution results
   * @param {Array<Object>} executionResults - Execution results
   * @returns {Promise<Array<Object>>} Results with metrics
   */
  async measureMetrics(executionResults) {
    try {
      this.logger.info('Measuring metrics', {
        results: executionResults.length,
        metrics: this.metrics.length
      });

      const resultsWithMetrics = [];

      for (const result of executionResults) {
        if (!result.success) {
          // Skip failed executions for metric calculation
          resultsWithMetrics.push({
            ...result,
            metrics: {}
          });
          continue;
        }

        try {
          const metricData = {
            executionTime: result.executionTime,
            output: result.output,
            metadata: result.metadata
          };

          const metricResults = await MetricsFactory.calculateAllMetrics(
            this.metrics, 
            metricData, 
            {
              prompt: result.prompt,
              agent: result.agent,
              runId: result.runId
            }
          );

          resultsWithMetrics.push({
            ...result,
            metrics: metricResults.results,
            metricErrors: metricResults.errors
          });

          this.logger.debug('Metrics calculated', { 
            prompt: result.prompt,
            agent: result.agent,
            runId: result.runId
          });

        } catch (error) {
          this.logger.warn('Failed to calculate metrics for result', { 
            prompt: result.prompt,
            agent: result.agent,
            runId: result.runId,
            error: error.message 
          });

          resultsWithMetrics.push({
            ...result,
            metrics: {},
            metricErrors: { general: error.message }
          });
        }
      }

      this.logger.info('Metrics measurement completed');
      return resultsWithMetrics;
    } catch (error) {
      this.logger.error('Failed to measure metrics', { error: error.message });
      throw error;
    }
  }

  /**
   * Save results to storage
   * @param {Array<Object>} results - Results with metrics
   * @returns {Promise<Object>} Saved results summary
   */
  async saveResults(results) {
    try {
      this.logger.info('Saving results');

      const filename = this.settings.output_filename || 
                      `llm_evaluator_${new Date().toISOString().split('T')[0]}`;

      // Create results object
      const resultsObject = await resultsStorage.createResults(filename, this.settings);

      // Add all results
      for (const result of results) {
        resultsStorage.addResult(
          resultsObject,
          result.prompt,
          result.agent,
          {
            runId: result.runId,
            executionTime: result.executionTime,
            success: result.success,
            error: result.error,
            timestamp: result.timestamp,
            ...result.metrics
          }
        );
      }

      // Save to file
      await resultsStorage.saveResults(filename, resultsObject);

      // Generate summary
      const summary = resultsStorage.exportSummary(resultsObject);

      this.logger.info('Results saved successfully', {
        filename: `${filename}.json`,
        totalRuns: results.length,
        successfulRuns: results.filter(r => r.success).length
      });

      return {
        filename,
        summary,
        totalRuns: results.length,
        successfulRuns: results.filter(r => r.success).length,
        failedRuns: results.filter(r => !r.success).length
      };
    } catch (error) {
      this.logger.error('Failed to save results', { error: error.message });
      throw error;
    }
  }

  /**
   * Clean up stage directory
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      this.logger.info('Cleaning up stage directory');

      if (await fileSystem.exists(this.stageDir)) {
        await fileSystem.remove(this.stageDir);
      }

      this.logger.info('Cleanup completed successfully');
    } catch (error) {
      this.logger.warn('Failed to cleanup stage directory', { error: error.message });
    }
  }

  /**
   * Get execution progress
   * @returns {Object} Progress information
   */
  getProgress() {
    // This would be implemented with proper progress tracking
    // For now, return basic information
    return {
      mode: 'LLM_Evaluator',
      agents: this.settings.agents,
      metrics: this.metrics.map(m => m.name),
      stage: 'ready'
    };
  }
}

module.exports = { LLMEvaluatorMode };
