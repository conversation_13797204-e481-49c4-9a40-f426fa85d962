const path = require('path');
const { logger } = require('../utils/Logger');
const { fileSystem } = require('../utils/FileSystem');
const { gitManager } = require('../utils/GitManager');
const { resultsStorage } = require('../utils/ResultsStorage');
const { AdapterFactory } = require('../adapters/AdapterFactory');
const { MetricsFactory } = require('../metrics/MetricsFactory');

/**
 * PR Recreate Mode - Evaluates AI assistants by recreating historical PRs
 * Workflow: clone repo, analyze git history, create worktrees, cherry-pick PRs, generate prompts, execute agents
 */
class PRRecreateMode {
  constructor(settings, options = {}) {
    this.settings = settings;
    this.options = {
      logger: logger,
      workingDir: './pr_recreate_workspace',
      ...options
    };

    this.logger = this.options.logger.child({ mode: 'PRRecreate' });
    this.workingDir = this.options.workingDir;
    
    this.validateSettings();
    this.initializeComponents();
  }

  /**
   * Validate settings for PR Recreate mode
   */
  validateSettings() {
    if (this.settings.mode !== 'PR_Recreate') {
      throw new Error('Settings mode must be PR_Recreate');
    }

    if (!this.settings.repo_url) {
      throw new Error('repo_url must be specified for PR_Recreate mode');
    }

    if (!this.settings.agents || this.settings.agents.length === 0) {
      throw new Error('At least one agent must be specified');
    }

    if (!this.settings.metrics || this.settings.metrics.length === 0) {
      throw new Error('At least one metric must be specified');
    }

    // Validate PR_Recreate specific settings
    if (!this.settings.PR_Recreate || !this.settings.PR_Recreate.num_prs) {
      throw new Error('PR_Recreate.num_prs must be specified');
    }

    this.logger.debug('Settings validated successfully');
  }

  /**
   * Initialize components
   */
  initializeComponents() {
    try {
      // Create adapters for each agent
      this.adapters = {};
      for (const agentName of this.settings.agents) {
        this.adapters[agentName] = AdapterFactory.createAdapter(agentName);
      }

      // Create metrics instances
      this.metrics = MetricsFactory.createMetricsFromSettings(this.settings);

      this.logger.info('Components initialized', {
        agents: this.settings.agents.length,
        metrics: this.metrics.length
      });
    } catch (error) {
      this.logger.error('Failed to initialize components', { error: error.message });
      throw error;
    }
  }

  /**
   * Execute PR Recreate mode workflow
   * @returns {Promise<Object>} Execution results
   */
  async execute() {
    try {
      this.logger.info('Starting PR Recreate mode execution');

      // Initialize working directory
      await this.initializeWorkspace();

      // Clone base repository
      const baseRepoPath = await this.cloneBaseRepository();

      // Analyze git history and find suitable PRs
      const selectedPRs = await this.analyzePRHistory(baseRepoPath);

      // Create worktrees for each agent
      const worktrees = await this.createWorktrees(baseRepoPath);

      // Execute PR recreation for each selected PR
      const executionResults = [];
      for (const pr of selectedPRs) {
        const prResults = await this.recreatePR(pr, baseRepoPath, worktrees);
        executionResults.push(...prResults);
      }

      // Measure metrics
      const metricResults = await this.measureMetrics(executionResults);

      // Save results
      const results = await this.saveResults(metricResults);

      this.logger.info('PR Recreate mode execution completed successfully', {
        prs: selectedPRs.length,
        totalRuns: executionResults.length,
        agents: this.settings.agents.length
      });

      return results;
    } catch (error) {
      this.logger.error('PR Recreate mode execution failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Initialize workspace directory
   * @returns {Promise<void>}
   */
  async initializeWorkspace() {
    try {
      this.logger.debug('Initializing workspace directory');

      // Create main working directory
      await fileSystem.ensureDir(this.workingDir);

      this.logger.debug('Workspace directory initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize workspace directory', { error: error.message });
      throw error;
    }
  }

  /**
   * Clone base repository
   * @returns {Promise<string>} Path to cloned repository
   */
  async cloneBaseRepository() {
    try {
      this.logger.info('Cloning base repository', { url: this.settings.repo_url });

      const repoPath = path.join(this.workingDir, 'base_repo');

      // Remove existing directory if it exists
      if (await fileSystem.exists(repoPath)) {
        await fileSystem.remove(repoPath);
      }

      // Clone repository
      await gitManager.clone(this.settings.repo_url, repoPath);

      // Checkout specific branch if specified
      if (this.settings.branch && this.settings.branch !== 'main') {
        await gitManager.checkout(repoPath, this.settings.branch);
      }

      this.logger.info('Base repository cloned successfully');
      return repoPath;
    } catch (error) {
      this.logger.error('Failed to clone base repository', { error: error.message });
      throw error;
    }
  }

  /**
   * Analyze PR history and select suitable PRs for recreation
   * @param {string} repoPath - Repository path
   * @returns {Promise<Array<Object>>} Selected PRs
   */
  async analyzePRHistory(repoPath) {
    try {
      this.logger.info('Analyzing PR history');

      // Get commit history
      const commitHistory = await gitManager.getCommitHistory(repoPath, { 
        limit: 500 
      });

      // Find merged PRs
      const mergedPRs = await this.findMergedPRs(repoPath, commitHistory);

      // Filter and select suitable PRs
      const suitablePRs = await this.filterSuitablePRs(repoPath, mergedPRs);

      // Select the specified number of PRs
      const selectedPRs = this.selectPRs(suitablePRs, this.settings.PR_Recreate.num_prs);

      this.logger.info('PR analysis completed', {
        totalCommits: commitHistory.length,
        mergedPRs: mergedPRs.length,
        suitablePRs: suitablePRs.length,
        selectedPRs: selectedPRs.length
      });

      return selectedPRs;
    } catch (error) {
      this.logger.error('Failed to analyze PR history', { error: error.message });
      throw error;
    }
  }

  /**
   * Find merged PRs from commit history
   * @param {string} repoPath - Repository path
   * @param {Array<Object>} commitHistory - Commit history
   * @returns {Promise<Array<Object>>} Merged PRs
   */
  async findMergedPRs(repoPath, commitHistory) {
    try {
      const mergedPRs = [];

      for (const commit of commitHistory) {
        // Look for merge commits that indicate PR merges
        if (this.isMergeCommit(commit)) {
          const prInfo = await this.extractPRInfo(repoPath, commit);
          if (prInfo) {
            mergedPRs.push(prInfo);
          }
        }
      }

      return mergedPRs;
    } catch (error) {
      this.logger.error('Failed to find merged PRs', { error: error.message });
      throw error;
    }
  }

  /**
   * Check if commit is a merge commit
   * @param {Object} commit - Commit object
   * @returns {boolean} True if merge commit
   */
  isMergeCommit(commit) {
    // Check for merge commit patterns
    const message = commit.message.toLowerCase();
    return message.includes('merge pull request') || 
           message.includes('merge branch') ||
           (commit.parents && commit.parents.length > 1);
  }

  /**
   * Extract PR information from merge commit
   * @param {string} repoPath - Repository path
   * @param {Object} commit - Merge commit
   * @returns {Promise<Object|null>} PR information
   */
  async extractPRInfo(repoPath, commit) {
    try {
      // Extract PR number from commit message
      const prNumberMatch = commit.message.match(/#(\d+)/);
      if (!prNumberMatch) {
        return null;
      }

      const prNumber = parseInt(prNumberMatch[1]);

      // Get the changes introduced by this PR
      const changes = await gitManager.getFileChanges(repoPath, commit.hash);

      // Get the parent commit (before the PR)
      const parentCommit = commit.parents ? commit.parents[0] : null;
      if (!parentCommit) {
        return null;
      }

      return {
        number: prNumber,
        title: this.extractPRTitle(commit.message),
        mergeCommit: commit.hash,
        parentCommit: parentCommit,
        author: commit.author,
        date: commit.date,
        message: commit.message,
        changes: changes,
        fileCount: changes.length,
        complexity: this.assessPRComplexity(changes)
      };
    } catch (error) {
      this.logger.warn('Failed to extract PR info', { 
        commitHash: commit.hash,
        error: error.message 
      });
      return null;
    }
  }

  /**
   * Extract PR title from commit message
   * @param {string} message - Commit message
   * @returns {string} PR title
   */
  extractPRTitle(message) {
    // Try to extract title from merge commit message
    const lines = message.split('\n');
    const firstLine = lines[0];
    
    // Remove "Merge pull request #123 from branch" prefix
    const titleMatch = firstLine.match(/Merge pull request #\d+ from .+?\s+(.+)/);
    if (titleMatch) {
      return titleMatch[1];
    }

    // Fallback to first line
    return firstLine.substring(0, 100);
  }

  /**
   * Assess PR complexity based on changes
   * @param {Array<Object>} changes - File changes
   * @returns {string} Complexity level
   */
  assessPRComplexity(changes) {
    const fileCount = changes.length;
    const totalLines = changes.reduce((sum, change) => 
      sum + (change.additions || 0) + (change.deletions || 0), 0
    );

    if (fileCount > 10 || totalLines > 500) {
      return 'high';
    } else if (fileCount > 3 || totalLines > 100) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * Filter PRs to find suitable ones for recreation
   * @param {string} repoPath - Repository path
   * @param {Array<Object>} prs - All merged PRs
   * @returns {Promise<Array<Object>>} Suitable PRs
   */
  async filterSuitablePRs(repoPath, prs) {
    const suitablePRs = [];

    for (const pr of prs) {
      try {
        // Filter criteria
        if (pr.fileCount === 0) continue; // Skip PRs with no file changes
        if (pr.fileCount > 20) continue; // Skip very large PRs
        if (pr.complexity === 'high') continue; // Skip overly complex PRs

        // Check if we can access the parent commit
        const parentExists = await this.commitExists(repoPath, pr.parentCommit);
        if (!parentExists) continue;

        // Check if changes are meaningful (not just documentation)
        const hasCodeChanges = await this.hasCodeChanges(pr.changes);
        if (!hasCodeChanges) continue;

        suitablePRs.push(pr);
      } catch (error) {
        this.logger.warn('Failed to evaluate PR suitability', { 
          prNumber: pr.number,
          error: error.message 
        });
      }
    }

    return suitablePRs;
  }

  /**
   * Check if commit exists in repository
   * @param {string} repoPath - Repository path
   * @param {string} commitHash - Commit hash
   * @returns {Promise<boolean>} True if commit exists
   */
  async commitExists(repoPath, commitHash) {
    try {
      // Try to get commit info
      await gitManager.getCommitInfo(repoPath, commitHash);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if PR has meaningful code changes
   * @param {Array<Object>} changes - File changes
   * @returns {boolean} True if has code changes
   */
  hasCodeChanges(changes) {
    const codeExtensions = ['.js', '.ts', '.py', '.java', '.cpp', '.c', '.go', '.rs', '.rb', '.php'];
    
    return changes.some(change => {
      const filename = change.filename || change.file || '';
      return codeExtensions.some(ext => filename.endsWith(ext));
    });
  }

  /**
   * Select PRs for recreation
   * @param {Array<Object>} suitablePRs - Suitable PRs
   * @param {number} numPRs - Number of PRs to select
   * @returns {Array<Object>} Selected PRs
   */
  selectPRs(suitablePRs, numPRs) {
    // Sort by complexity and date to get a good mix
    const sorted = suitablePRs.sort((a, b) => {
      // Prefer medium complexity PRs
      const complexityScore = (pr) => {
        switch (pr.complexity) {
          case 'low': return 1;
          case 'medium': return 3;
          case 'high': return 2;
          default: return 0;
        }
      };

      const scoreA = complexityScore(a);
      const scoreB = complexityScore(b);
      
      if (scoreA !== scoreB) {
        return scoreB - scoreA;
      }

      // Then by date (more recent first)
      return new Date(b.date) - new Date(a.date);
    });

    return sorted.slice(0, numPRs);
  }

  /**
   * Create worktrees for each agent
   * @param {string} baseRepoPath - Base repository path
   * @returns {Promise<Object>} Worktree paths by agent
   */
  async createWorktrees(baseRepoPath) {
    try {
      this.logger.info('Creating worktrees for agents');

      const worktrees = {};

      for (const agentName of this.settings.agents) {
        const worktreePath = path.join(this.workingDir, `worktree_${agentName}`);
        
        // Remove existing worktree if it exists
        if (await fileSystem.exists(worktreePath)) {
          await fileSystem.remove(worktreePath);
        }

        // Create worktree
        await gitManager.createWorktree(baseRepoPath, worktreePath, 'main');
        
        worktrees[agentName] = worktreePath;
      }

      this.logger.info('Worktrees created successfully');
      return worktrees;
    } catch (error) {
      this.logger.error('Failed to create worktrees', { error: error.message });
      throw error;
    }
  }

  /**
   * Recreate a single PR across all agents
   * @param {Object} pr - PR information
   * @param {string} baseRepoPath - Base repository path
   * @param {Object} worktrees - Worktree paths by agent
   * @returns {Promise<Array<Object>>} PR recreation results
   */
  async recreatePR(pr, baseRepoPath, worktrees) {
    try {
      this.logger.info('Recreating PR', { prNumber: pr.number, title: pr.title });

      const prResults = [];

      // Create human branch with the actual PR changes
      const humanBranchName = `human_pr_${pr.number}`;
      await this.createHumanBranch(baseRepoPath, pr, humanBranchName);

      // Generate prompt from PR metadata
      const prompt = await this.generatePRPrompt(pr, baseRepoPath);

      // Execute each agent
      for (const agentName of this.settings.agents) {
        const agentResults = await this.executeAgentOnPR(
          pr, 
          prompt, 
          agentName, 
          worktrees[agentName],
          humanBranchName
        );
        prResults.push(...agentResults);
      }

      return prResults;
    } catch (error) {
      this.logger.error('Failed to recreate PR', { 
        prNumber: pr.number,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Create human branch with actual PR changes
   * @param {string} repoPath - Repository path
   * @param {Object} pr - PR information
   * @param {string} branchName - Branch name
   * @returns {Promise<void>}
   */
  async createHumanBranch(repoPath, pr, branchName) {
    try {
      this.logger.debug('Creating human branch', { 
        prNumber: pr.number,
        branchName 
      });

      // Checkout parent commit
      await gitManager.checkout(repoPath, pr.parentCommit);

      // Create and checkout new branch
      await gitManager.createBranch(repoPath, branchName);

      // Cherry-pick the merge commit to get the changes
      await gitManager.cherryPick(repoPath, pr.mergeCommit);

      this.logger.debug('Human branch created successfully', { branchName });
    } catch (error) {
      this.logger.error('Failed to create human branch', { 
        prNumber: pr.number,
        branchName,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Generate prompt from PR metadata
   * @param {Object} pr - PR information
   * @param {string} repoPath - Repository path
   * @returns {Promise<string>} Generated prompt
   */
  async generatePRPrompt(pr, repoPath) {
    try {
      // Get diff between parent and merge commit
      const diff = await gitManager.getDiff(repoPath, pr.parentCommit, pr.mergeCommit);

      // Analyze the changes to understand what was done
      const changeAnalysis = this.analyzePRChanges(pr.changes, diff);

      // Generate descriptive prompt
      const prompt = this.buildPRPrompt(pr, changeAnalysis);

      this.logger.debug('PR prompt generated', { 
        prNumber: pr.number,
        promptLength: prompt.length 
      });

      return prompt;
    } catch (error) {
      this.logger.error('Failed to generate PR prompt', { 
        prNumber: pr.number,
        error: error.message 
      });
      
      // Fallback to basic prompt
      return `Implement the changes described in PR #${pr.number}: ${pr.title}`;
    }
  }

  /**
   * Analyze PR changes to understand the intent
   * @param {Array<Object>} changes - File changes
   * @param {string} diff - Git diff
   * @returns {Object} Change analysis
   */
  analyzePRChanges(changes, diff) {
    const analysis = {
      filesModified: changes.length,
      linesAdded: 0,
      linesDeleted: 0,
      fileTypes: new Set(),
      changeTypes: new Set()
    };

    // Analyze file changes
    for (const change of changes) {
      analysis.linesAdded += change.additions || 0;
      analysis.linesDeleted += change.deletions || 0;

      const filename = change.filename || change.file || '';
      const extension = filename.split('.').pop();
      if (extension) {
        analysis.fileTypes.add(extension);
      }

      // Determine change type
      if (change.status === 'added') {
        analysis.changeTypes.add('addition');
      } else if (change.status === 'removed') {
        analysis.changeTypes.add('deletion');
      } else {
        analysis.changeTypes.add('modification');
      }
    }

    return analysis;
  }

  /**
   * Build PR prompt from analysis
   * @param {Object} pr - PR information
   * @param {Object} analysis - Change analysis
   * @returns {string} Generated prompt
   */
  buildPRPrompt(pr, analysis) {
    let prompt = `Implement the following changes to recreate PR #${pr.number}:\n\n`;
    prompt += `Title: ${pr.title}\n\n`;
    
    prompt += `Summary:\n`;
    prompt += `- Modify ${analysis.filesModified} file(s)\n`;
    prompt += `- Add ${analysis.linesAdded} line(s)\n`;
    prompt += `- Remove ${analysis.linesDeleted} line(s)\n`;
    prompt += `- File types: ${Array.from(analysis.fileTypes).join(', ')}\n`;
    prompt += `- Change types: ${Array.from(analysis.changeTypes).join(', ')}\n\n`;

    prompt += `Requirements:\n`;
    prompt += `- Implement the functionality described in the PR title\n`;
    prompt += `- Ensure the changes are functionally equivalent to the original PR\n`;
    prompt += `- Follow the existing code style and patterns\n`;
    prompt += `- Test your implementation to ensure it works correctly\n\n`;

    prompt += `Note: This is a recreation task. Implement the changes that would achieve the same goal as the original PR.`;

    return prompt;
  }

  /**
   * Execute agent on PR recreation task
   * @param {Object} pr - PR information
   * @param {string} prompt - Generated prompt
   * @param {string} agentName - Agent name
   * @param {string} worktreePath - Agent worktree path
   * @param {string} humanBranchName - Human branch name
   * @returns {Promise<Array<Object>>} Agent execution results
   */
  async executeAgentOnPR(pr, prompt, agentName, worktreePath, humanBranchName) {
    try {
      this.logger.debug('Executing agent on PR', { 
        prNumber: pr.number,
        agentName 
      });

      const adapter = this.adapters[agentName];
      const results = [];

      // Reset worktree to parent commit
      await gitManager.checkout(worktreePath, pr.parentCommit);

      // Create agent branch
      const agentBranchName = `agent_${agentName}_pr_${pr.number}`;
      await gitManager.createBranch(worktreePath, agentBranchName);

      for (let runId = 1; runId <= this.settings.runs_per_prompt; runId++) {
        try {
          this.logger.debug('Executing agent run', { 
            agentName,
            prNumber: pr.number,
            runId 
          });

          const startTime = Date.now();

          // Execute agent
          const result = await adapter.execute(prompt, {
            workingDirectory: worktreePath,
            prNumber: pr.number,
            runId: runId,
            timeout: this.settings.execution?.timeout || 600000
          });

          const endTime = Date.now();
          const executionTime = endTime - startTime;

          // Get the changes made by the agent
          const agentChanges = await gitManager.getFileChanges(worktreePath);

          results.push({
            pr: pr.number,
            agent: agentName,
            runId: runId,
            executionTime: executionTime,
            output: result.output,
            metadata: result.metadata,
            agentChanges: agentChanges,
            humanBranch: humanBranchName,
            agentBranch: agentBranchName,
            success: true,
            timestamp: new Date().toISOString()
          });

          this.logger.debug('Agent run completed successfully', { 
            agentName,
            prNumber: pr.number,
            runId,
            executionTime: `${executionTime}ms`
          });

        } catch (error) {
          this.logger.warn('Agent run failed', { 
            agentName,
            prNumber: pr.number,
            runId,
            error: error.message 
          });

          results.push({
            pr: pr.number,
            agent: agentName,
            runId: runId,
            executionTime: null,
            output: null,
            metadata: null,
            agentChanges: null,
            humanBranch: humanBranchName,
            agentBranch: agentBranchName,
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
          });
        }

        // Reset for next run
        if (runId < this.settings.runs_per_prompt) {
          await gitManager.checkout(worktreePath, pr.parentCommit);
          await gitManager.deleteBranch(worktreePath, agentBranchName);
          await gitManager.createBranch(worktreePath, agentBranchName);
        }
      }

      return results;
    } catch (error) {
      this.logger.error('Failed to execute agent on PR', { 
        prNumber: pr.number,
        agentName,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Measure metrics for execution results
   * @param {Array<Object>} executionResults - Execution results
   * @returns {Promise<Array<Object>>} Results with metrics
   */
  async measureMetrics(executionResults) {
    try {
      this.logger.info('Measuring metrics', {
        results: executionResults.length,
        metrics: this.metrics.length
      });

      const resultsWithMetrics = [];

      for (const result of executionResults) {
        if (!result.success) {
          // Skip failed executions for metric calculation
          resultsWithMetrics.push({
            ...result,
            metrics: {}
          });
          continue;
        }

        try {
          const metricData = {
            executionTime: result.executionTime,
            output: result.output,
            metadata: result.metadata,
            agentChanges: result.agentChanges,
            humanBranch: result.humanBranch,
            agentBranch: result.agentBranch
          };

          const metricResults = await MetricsFactory.calculateAllMetrics(
            this.metrics, 
            metricData, 
            {
              pr: result.pr,
              agent: result.agent,
              runId: result.runId
            }
          );

          resultsWithMetrics.push({
            ...result,
            metrics: metricResults.results,
            metricErrors: metricResults.errors
          });

          this.logger.debug('Metrics calculated', { 
            pr: result.pr,
            agent: result.agent,
            runId: result.runId
          });

        } catch (error) {
          this.logger.warn('Failed to calculate metrics for result', { 
            pr: result.pr,
            agent: result.agent,
            runId: result.runId,
            error: error.message 
          });

          resultsWithMetrics.push({
            ...result,
            metrics: {},
            metricErrors: { general: error.message }
          });
        }
      }

      this.logger.info('Metrics measurement completed');
      return resultsWithMetrics;
    } catch (error) {
      this.logger.error('Failed to measure metrics', { error: error.message });
      throw error;
    }
  }

  /**
   * Save results to storage
   * @param {Array<Object>} results - Results with metrics
   * @returns {Promise<Object>} Saved results summary
   */
  async saveResults(results) {
    try {
      this.logger.info('Saving results');

      const filename = this.settings.output_filename || 
                      `pr_recreate_${new Date().toISOString().split('T')[0]}`;

      // Create results object
      const resultsObject = await resultsStorage.createResults(filename, this.settings);

      // Add all results
      for (const result of results) {
        resultsStorage.addResult(
          resultsObject,
          `PR_${result.pr}`,
          result.agent,
          {
            runId: result.runId,
            executionTime: result.executionTime,
            success: result.success,
            error: result.error,
            timestamp: result.timestamp,
            humanBranch: result.humanBranch,
            agentBranch: result.agentBranch,
            ...result.metrics
          }
        );
      }

      // Save to file
      await resultsStorage.saveResults(filename, resultsObject);

      // Generate summary
      const summary = resultsStorage.exportSummary(resultsObject);

      this.logger.info('Results saved successfully', {
        filename: `${filename}.json`,
        totalRuns: results.length,
        successfulRuns: results.filter(r => r.success).length
      });

      return {
        filename,
        summary,
        totalRuns: results.length,
        successfulRuns: results.filter(r => r.success).length,
        failedRuns: results.filter(r => !r.success).length
      };
    } catch (error) {
      this.logger.error('Failed to save results', { error: error.message });
      throw error;
    }
  }

  /**
   * Clean up workspace
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      this.logger.info('Cleaning up workspace');

      if (await fileSystem.exists(this.workingDir)) {
        await fileSystem.remove(this.workingDir);
      }

      this.logger.info('Cleanup completed successfully');
    } catch (error) {
      this.logger.warn('Failed to cleanup workspace', { error: error.message });
    }
  }

  /**
   * Get execution progress
   * @returns {Object} Progress information
   */
  getProgress() {
    // This would be implemented with proper progress tracking
    // For now, return basic information
    return {
      mode: 'PR_Recreate',
      agents: this.settings.agents,
      metrics: this.metrics.map(m => m.name),
      stage: 'ready'
    };
  }
}

module.exports = { PRRecreateMode };
